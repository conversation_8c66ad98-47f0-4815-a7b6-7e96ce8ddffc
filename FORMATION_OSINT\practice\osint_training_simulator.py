#!/usr/bin/env python3
"""
Simulateur d'Entraînement OSINT Master
Formation complète pour devenir expert en OSINT CTF

Ce simulateur offre un environnement d'entraînement complet avec :
- Scénarios réalistes
- Outils intégrés
- Méthodologie guidée
- Évaluation des performances
"""

import random
import json
import webbrowser
import urllib.parse
from datetime import datetime, timedelta

class OSINTTrainingSimulator:
    """Simulateur d'entraînement OSINT complet"""
    
    def __init__(self):
        self.score = 0
        self.session_stats = {
            'challenges_completed': 0,
            'success_rate': 0,
            'time_started': datetime.now(),
            'techniques_used': []
        }
        
        # Base de données d'entraînement
        self.training_data = {
            'personas': [
                {
                    'name': '<PERSON>',
                    'email': '<EMAIL>',
                    'linkedin': 'linkedin.com/in/alex-martin-dev',
                    'twitter': '@alexm_dev',
                    'github': 'github.com/alexmartin',
                    'company': 'TechStart Solutions',
                    'location': 'Lyon, France',
                    'skills': ['Python', 'JavaScript', 'DevOps']
                },
                {
                    'name': '<PERSON> Chen',
                    'email': '<EMAIL>',
                    'linkedin': 'linkedin.com/in/sarah-chen-security',
                    'twitter': '@sarahc_sec',
                    'company': 'CyberSec Corp',
                    'location': 'Singapore',
                    'skills': ['Cybersecurity', 'Penetration Testing', 'OSINT']
                }
            ],
            'companies': [
                {
                    'name': 'TechStart Solutions',
                    'domain': 'techstart.io',
                    'employees': ['Alex Martin', 'Julie Dubois', 'Marc Leroy'],
                    'location': 'Lyon, France',
                    'founded': '2019',
                    'industry': 'Software Development'
                },
                {
                    'name': 'CyberSec Corp',
                    'domain': 'cybersec.com',
                    'employees': ['Sarah Chen', 'Mike Johnson', 'Lisa Wang'],
                    'location': 'Singapore',
                    'founded': '2015',
                    'industry': 'Cybersecurity'
                }
            ],
            'locations': [
                {
                    'name': 'Tour Part-Dieu, Lyon',
                    'coordinates': '45.7640, 4.8357',
                    'description': 'Gratte-ciel emblématique de Lyon',
                    'hints': ['Plus haut bâtiment de Lyon', 'Quartier d\'affaires', 'Forme cylindrique']
                },
                {
                    'name': 'Marina Bay Sands, Singapore',
                    'coordinates': '1.2834, 103.8607',
                    'description': 'Complexe hôtelier iconique',
                    'hints': ['Piscine à débordement sur le toit', 'Trois tours', 'Architecture futuriste']
                }
            ]
        }
    
    def show_main_menu(self):
        """Affiche le menu principal"""
        print("🕵️ SIMULATEUR D'ENTRAÎNEMENT OSINT MASTER")
        print("=" * 60)
        print("Formation complète pour maîtriser l'OSINT et réussir les CTF")
        print("=" * 60)
        print()
        print("🎯 MODES D'ENTRAÎNEMENT:")
        print("1. 👤 OSINT sur les Personnes")
        print("2. 🏢 OSINT sur les Entreprises") 
        print("3. 🌍 Géolocalisation et GEOINT")
        print("4. 💻 OSINT Technique")
        print("5. 🔗 Corrélation Multi-Sources")
        print("6. ⚡ Speed OSINT (Entraînement CTF)")
        print("7. 📊 Statistiques et Progression")
        print("8. 🛠️ Outils et Ressources")
        print("9. ❌ Quitter")
        print()
    
    def person_osint_training(self):
        """Entraînement OSINT sur les personnes"""
        print("\n👤 ENTRAÎNEMENT OSINT - PERSONNES")
        print("=" * 50)
        
        # Sélectionner une persona aléatoire
        persona = random.choice(self.training_data['personas'])
        
        print(f"🎯 CIBLE: {persona['name']}")
        print("📋 INFORMATIONS INITIALES:")
        print(f"  • Email: {persona['email']}")
        print()
        
        print("🔍 VOTRE MISSION:")
        print("Trouvez un maximum d'informations sur cette personne")
        print("en utilisant uniquement des sources publiques.")
        print()
        
        # Questions progressives
        questions = [
            {
                'question': f"Sur quel réseau professionnel pourriez-vous trouver {persona['name']} ?",
                'answer': 'linkedin',
                'hint': 'Réseau social professionnel le plus populaire',
                'points': 10
            },
            {
                'question': f"Dans quelle entreprise travaille {persona['name']} ?",
                'answer': persona['company'],
                'hint': f"Regardez le domaine de l'email: {persona['email']}",
                'points': 15
            },
            {
                'question': f"Dans quelle ville se trouve {persona['name']} ?",
                'answer': persona['location'].split(',')[0],
                'hint': 'Vérifiez le profil LinkedIn ou les métadonnées',
                'points': 10
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"\n❓ Question {i} ({q['points']} points):")
            print(q['question'])
            
            # Proposer un indice
            show_hint = input("Voulez-vous un indice ? (y/n): ").lower().strip()
            if show_hint == 'y':
                print(f"💡 Indice: {q['hint']}")
                q['points'] = max(5, q['points'] - 5)  # Réduire les points si indice utilisé
            
            answer = input("Votre réponse: ").strip()
            
            if answer.lower() in q['answer'].lower():
                print(f"✅ Correct ! +{q['points']} points")
                score += q['points']
                self.session_stats['techniques_used'].append('person_research')
            else:
                print(f"❌ Incorrect. Réponse: {q['answer']}")
        
        # Bonus pour techniques avancées
        print(f"\n🎯 QUESTIONS BONUS:")
        bonus_questions = [
            {
                'question': 'Quel outil utiliseriez-vous pour vérifier si cet email a été compromis ?',
                'answer': 'have i been pwned',
                'points': 5
            },
            {
                'question': 'Comment pourriez-vous trouver d\'autres comptes de cette personne ?',
                'answer': 'sherlock',
                'points': 5
            }
        ]
        
        for q in bonus_questions:
            print(f"\n💎 Bonus: {q['question']}")
            answer = input("Votre réponse: ").strip()
            if any(word in answer.lower() for word in q['answer'].split()):
                print(f"✅ Bonus ! +{q['points']} points")
                score += q['points']
        
        self.score += score
        self.session_stats['challenges_completed'] += 1
        
        print(f"\n📊 Score de cette session: {score} points")
        print(f"📈 Score total: {self.score} points")
        
        # Conseils personnalisés
        if score >= 35:
            print("🏆 Excellent ! Vous maîtrisez l'OSINT sur les personnes.")
        elif score >= 25:
            print("👍 Bien ! Continuez à pratiquer les techniques avancées.")
        else:
            print("📚 Étudiez les guides sur l'OSINT personnel.")
    
    def company_osint_training(self):
        """Entraînement OSINT sur les entreprises"""
        print("\n🏢 ENTRAÎNEMENT OSINT - ENTREPRISES")
        print("=" * 50)
        
        company = random.choice(self.training_data['companies'])
        
        print(f"🎯 CIBLE: {company['name']}")
        print(f"🌐 DOMAINE: {company['domain']}")
        print()
        
        print("🔍 VOTRE MISSION:")
        print("Collectez des informations sur cette entreprise")
        print("et son infrastructure numérique.")
        print()
        
        questions = [
            {
                'question': 'Quelle commande utiliseriez-vous pour obtenir les informations WHOIS ?',
                'answer': f"whois {company['domain']}",
                'points': 10,
                'flexible': True
            },
            {
                'question': 'Quel outil utiliseriez-vous pour trouver les sous-domaines ?',
                'answer': 'sublist3r',
                'points': 15,
                'flexible': True
            },
            {
                'question': f"En quelle année {company['name']} a-t-elle été fondée ?",
                'answer': company['founded'],
                'points': 10
            },
            {
                'question': 'Comment trouveriez-vous les employés de cette entreprise ?',
                'answer': 'linkedin',
                'points': 15,
                'flexible': True
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"\n❓ Question {i} ({q['points']} points):")
            print(q['question'])
            
            answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                if any(word in answer.lower() for word in q['answer'].lower().split()):
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            else:
                if answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
        
        self.score += score
        self.session_stats['challenges_completed'] += 1
        print(f"\n📊 Score de cette session: {score} points")
    
    def geolocation_training(self):
        """Entraînement géolocalisation"""
        print("\n🌍 ENTRAÎNEMENT GÉOLOCALISATION")
        print("=" * 50)
        
        location = random.choice(self.training_data['locations'])
        
        print("🎯 DÉFI GÉOLOCALISATION:")
        print("Identifiez le lieu à partir des indices suivants:")
        print()
        
        for i, hint in enumerate(location['hints'], 1):
            print(f"  {i}. {hint}")
        print()
        
        questions = [
            {
                'question': 'Quel est le nom de ce lieu ?',
                'answer': location['name'].split(',')[0],
                'points': 20,
                'flexible': True
            },
            {
                'question': 'Dans quelle ville se trouve-t-il ?',
                'answer': location['name'].split(',')[1].strip(),
                'points': 15
            },
            {
                'question': 'Quelles sont ses coordonnées approximatives ?',
                'answer': location['coordinates'],
                'points': 10,
                'flexible': True
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"\n❓ Question {i} ({q['points']} points):")
            print(q['question'])
            
            answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                if any(word in answer.lower() for word in q['answer'].lower().split()):
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            else:
                if answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
        
        self.score += score
        self.session_stats['challenges_completed'] += 1
        print(f"\n📊 Score de cette session: {score} points")
    
    def speed_osint_training(self):
        """Entraînement Speed OSINT pour CTF"""
        print("\n⚡ SPEED OSINT - ENTRAÎNEMENT CTF")
        print("=" * 50)
        print("🏁 Résolvez le maximum de challenges en 5 minutes !")
        print("⏰ Chaque seconde compte en CTF...")
        print()
        
        # Challenges rapides
        speed_challenges = [
            {
                'question': 'Quel opérateur Google pour chercher des PDF sur un site ?',
                'answer': 'filetype:pdf',
                'points': 5
            },
            {
                'question': 'Outil pour vérifier si un email a été compromis ?',
                'answer': 'have i been pwned',
                'points': 5,
                'flexible': True
            },
            {
                'question': 'Commande pour obtenir les infos WHOIS d\'un domaine ?',
                'answer': 'whois',
                'points': 5,
                'flexible': True
            },
            {
                'question': 'Site pour recherche inversée d\'images ?',
                'answer': 'tineye',
                'points': 5,
                'flexible': True
            },
            {
                'question': 'Outil pour trouver des usernames sur 400+ sites ?',
                'answer': 'sherlock',
                'points': 5,
                'flexible': True
            }
        ]
        
        start_time = datetime.now()
        score = 0
        
        for i, challenge in enumerate(speed_challenges, 1):
            elapsed = (datetime.now() - start_time).seconds
            if elapsed >= 300:  # 5 minutes
                print("⏰ Temps écoulé !")
                break
                
            print(f"\n⚡ Challenge {i}/5 ({challenge['points']} pts) - {300-elapsed}s restantes")
            print(challenge['question'])
            
            answer = input("Réponse rapide: ").strip()
            
            if challenge.get('flexible'):
                if any(word in answer.lower() for word in challenge['answer'].lower().split()):
                    print("✅ Correct !")
                    score += challenge['points']
                else:
                    print(f"❌ Réponse: {challenge['answer']}")
            else:
                if answer.lower() == challenge['answer'].lower():
                    print("✅ Correct !")
                    score += challenge['points']
                else:
                    print(f"❌ Réponse: {challenge['answer']}")
        
        total_time = (datetime.now() - start_time).seconds
        self.score += score
        
        print(f"\n🏁 RÉSULTATS SPEED OSINT:")
        print(f"⏱️ Temps total: {total_time} secondes")
        print(f"📊 Score: {score} points")
        print(f"⚡ Vitesse: {score/max(1, total_time)*60:.1f} points/minute")
        
        if score >= 20:
            print("🏆 Excellent ! Vous êtes prêt pour les CTF !")
        elif score >= 15:
            print("👍 Bien ! Travaillez votre vitesse.")
        else:
            print("📚 Révisez les bases avant les CTF.")
    
    def show_statistics(self):
        """Affiche les statistiques de progression"""
        print("\n📊 STATISTIQUES DE PROGRESSION")
        print("=" * 50)
        
        session_time = datetime.now() - self.session_stats['time_started']
        
        print(f"🎯 Score total: {self.score} points")
        print(f"🏆 Challenges complétés: {self.session_stats['challenges_completed']}")
        print(f"⏱️ Temps de session: {session_time.seconds//60} minutes")
        
        if self.session_stats['challenges_completed'] > 0:
            avg_score = self.score / self.session_stats['challenges_completed']
            print(f"📈 Score moyen par challenge: {avg_score:.1f} points")
        
        print(f"\n🛠️ Techniques utilisées:")
        techniques = set(self.session_stats['techniques_used'])
        for technique in techniques:
            print(f"  • {technique}")
        
        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")
        if self.score >= 100:
            print("  🏆 Niveau expert ! Participez aux CTF.")
        elif self.score >= 50:
            print("  📚 Continuez l'entraînement régulier.")
        else:
            print("  🔄 Révisez les guides fondamentaux.")
    
    def show_tools_resources(self):
        """Affiche les outils et ressources"""
        print("\n🛠️ OUTILS ET RESSOURCES OSINT")
        print("=" * 50)
        
        tools = {
            'Recherche': ['Google Dorking', 'DuckDuckGo', 'Yandex'],
            'Personnes': ['Sherlock', 'Pipl', 'Have I Been Pwned'],
            'Domaines': ['WHOIS', 'Sublist3r', 'theHarvester'],
            'Images': ['TinEye', 'Google Images', 'Exiftool'],
            'Géolocalisation': ['Google Earth', 'Wikimapia', 'SunCalc'],
            'Réseaux sociaux': ['Social-Searcher', 'Twint', 'Instagram OSINT']
        }
        
        for category, tool_list in tools.items():
            print(f"\n🔧 {category}:")
            for tool in tool_list:
                print(f"  • {tool}")
        
        print(f"\n📚 RESSOURCES D'APPRENTISSAGE:")
        print("  • OSINT Framework (osintframework.com)")
        print("  • Bellingcat Online Investigation Toolkit")
        print("  • SANS FOR578 (Cyber Threat Intelligence)")
        print("  • IntelTechniques.com")
    
    def run_simulator(self):
        """Lance le simulateur principal"""
        print("🚀 Bienvenue dans le Simulateur OSINT Master !")
        print("Développez vos compétences pour réussir les CTF OSINT.")
        print()
        
        while True:
            self.show_main_menu()
            
            try:
                choice = input("Choisissez votre entraînement (1-9): ").strip()
            except KeyboardInterrupt:
                print("\n👋 Au revoir !")
                break
            
            if choice == '1':
                self.person_osint_training()
            elif choice == '2':
                self.company_osint_training()
            elif choice == '3':
                self.geolocation_training()
            elif choice == '4':
                print("💻 OSINT Technique - En développement...")
            elif choice == '5':
                print("🔗 Corrélation Multi-Sources - En développement...")
            elif choice == '6':
                self.speed_osint_training()
            elif choice == '7':
                self.show_statistics()
            elif choice == '8':
                self.show_tools_resources()
            elif choice == '9':
                print("👋 Au revoir ! Continuez à pratiquer l'OSINT !")
                break
            else:
                print("❌ Choix invalide. Essayez encore.")
            
            input("\nAppuyez sur Entrée pour continuer...")

def main():
    """Fonction principale"""
    simulator = OSINTTrainingSimulator()
    simulator.run_simulator()

if __name__ == "__main__":
    main()

"""
FONCTIONNALITÉS DU SIMULATEUR:

1. ENTRAÎNEMENT MULTI-DOMAINES:
   - OSINT personnel
   - OSINT entreprise
   - Géolocalisation
   - Speed OSINT pour CTF

2. SYSTÈME DE SCORING:
   - Points par bonne réponse
   - Bonus pour techniques avancées
   - Statistiques de progression

3. DONNÉES RÉALISTES:
   - Personas fictives
   - Entreprises simulées
   - Lieux géolocalisables

4. PRÉPARATION CTF:
   - Entraînement chronométré
   - Questions type CTF
   - Techniques de vitesse

Ce simulateur prépare efficacement aux challenges
OSINT des CTF et aux investigations réelles.
"""
