#!/usr/bin/env python3
"""
Script pour organiser les formations PWN et OSINT
"""

import os
import shutil

def create_directory_structure():
    """Crée la structure de dossiers pour les formations"""
    
    # Structure des dossiers
    structure = {
        'FORMATION_PWN': [
            'challenges',
            'exploits', 
            'guides',
            'practice',
            'tools'
        ],
        'FORMATION_OSINT': [
            'challenges',
            'tools',
            'guides', 
            'practice',
            'resources',
            'datasets'
        ]
    }
    
    # Créer les dossiers
    for main_folder, subfolders in structure.items():
        if not os.path.exists(main_folder):
            os.makedirs(main_folder)
            print(f"✅ Créé: {main_folder}/")
        
        for subfolder in subfolders:
            path = os.path.join(main_folder, subfolder)
            if not os.path.exists(path):
                os.makedirs(path)
                print(f"✅ Créé: {path}/")

def move_pwn_files():
    """Déplace les fichiers PWN existants dans le bon dossier"""
    
    # Mapping des fichiers à déplacer
    moves = {
        # Fichiers racine vers FORMATION_PWN
        'FORMATION_PWN.md': 'FORMATION_PWN/',
        'PROGRESSION_FORMATION.md': 'FORMATION_PWN/',
        'setup_pwn_env.sh': 'FORMATION_PWN/tools/',
        'install_mingw.ps1': 'FORMATION_PWN/tools/',
        
        # Dossiers vers FORMATION_PWN
        'challenges': 'FORMATION_PWN/',
        'exploits': 'FORMATION_PWN/',
        'guides': 'FORMATION_PWN/',
        'practice': 'FORMATION_PWN/'
    }
    
    for source, dest_dir in moves.items():
        if os.path.exists(source):
            dest_path = os.path.join(dest_dir, os.path.basename(source))
            
            # Si c'est un dossier et qu'il existe déjà dans la destination
            if os.path.isdir(source) and os.path.exists(dest_path):
                # Déplacer le contenu
                for item in os.listdir(source):
                    src_item = os.path.join(source, item)
                    dst_item = os.path.join(dest_path, item)
                    if os.path.exists(dst_item):
                        if os.path.isfile(dst_item):
                            os.remove(dst_item)
                        else:
                            shutil.rmtree(dst_item)
                    shutil.move(src_item, dst_item)
                os.rmdir(source)
            else:
                # Déplacer directement
                if os.path.exists(dest_path):
                    if os.path.isfile(dest_path):
                        os.remove(dest_path)
                    else:
                        shutil.rmtree(dest_path)
                shutil.move(source, dest_path)
            
            print(f"📁 Déplacé: {source} -> {dest_path}")

if __name__ == "__main__":
    print("🚀 Organisation des formations...")
    print("=" * 40)
    
    create_directory_structure()
    print()
    move_pwn_files()
    
    print("\n✅ Organisation terminée !")
    print("\n📁 Structure créée:")
    print("├── FORMATION_PWN/")
    print("│   ├── challenges/")
    print("│   ├── exploits/")
    print("│   ├── guides/")
    print("│   ├── practice/")
    print("│   └── tools/")
    print("└── FORMATION_OSINT/")
    print("    ├── challenges/")
    print("    ├── tools/")
    print("    ├── guides/")
    print("    ├── practice/")
    print("    ├── resources/")
    print("    └── datasets/")
