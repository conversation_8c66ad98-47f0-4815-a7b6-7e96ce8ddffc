#!/bin/bash

# Script de configuration complète pour environnement de pwning CTF
# Auteur: Assistant IA pour formation PWN

echo "🚀 Configuration de l'environnement de pwning pour CTF..."
echo "=================================================="

# Mise à jour du système
echo "📦 Mise à jour du système..."
sudo apt update && sudo apt upgrade -y

# Installation des outils de base
echo "🔧 Installation des outils de base..."
sudo apt install -y \
    build-essential \
    gcc-multilib \
    g++-multilib \
    libc6-dev-i386 \
    gdb \
    gdb-multiarch \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    vim \
    nano \
    file \
    binutils \
    strace \
    ltrace \
    objdump \
    readelf \
    hexdump \
    xxd \
    strings \
    checksec \
    one_gadget \
    ropper \
    nasm \
    yasm \
    netcat \
    socat \
    tmux \
    tree \
    unzip

# Installation de pwntools (outil ESSENTIEL pour CTF)
echo "🐍 Installation de pwntools..."
pip3 install --upgrade pip
pip3 install pwntools
pip3 install ropper
pip3 install capstone
pip3 install keystone-engine
pip3 install unicorn
pip3 install r2pipe

# Installation de GDB avec plugins essentiels
echo "🔍 Configuration de GDB avec plugins..."

# GEF (GDB Enhanced Features) - Plugin GDB incontournable
wget -O ~/.gdbinit-gef.py -q https://gef.blah.cat/py
echo "source ~/.gdbinit-gef.py" >> ~/.gdbinit

# Peda (alternative à GEF)
git clone https://github.com/longld/peda.git ~/peda
echo "# source ~/peda/peda.py" >> ~/.gdbinit

# Installation de radare2
echo "🔬 Installation de radare2..."
git clone https://github.com/radareorg/radare2
cd radare2 && sys/install.sh
cd ..

# Installation de Ghidra (si Java est disponible)
echo "👻 Vérification de Java pour Ghidra..."
if command -v java &> /dev/null; then
    echo "Java détecté, installation de Ghidra..."
    wget https://github.com/NationalSecurityAgency/ghidra/releases/download/Ghidra_10.4_build/ghidra_10.4_PUBLIC_20230928.zip
    unzip ghidra_10.4_PUBLIC_20230928.zip
    mv ghidra_10.4_PUBLIC ~/ghidra
    rm ghidra_10.4_PUBLIC_20230928.zip
else
    echo "Java non détecté, installation de OpenJDK..."
    sudo apt install -y openjdk-17-jdk
fi

# Configuration de l'environnement
echo "⚙️ Configuration de l'environnement..."

# Ajout d'alias utiles
cat >> ~/.bashrc << 'EOF'

# Alias pour pwning
alias gdb='gdb -q'
alias py='python3'
alias pwntools='python3 -c "from pwn import *"'
alias checksec='checksec --file'
alias disas='objdump -d -M intel'
alias strings='strings -a'
alias hexdump='hexdump -C'

# Variables d'environnement
export PWNLIB_NOTERM=1
export PAGER=less

# Fonction pour compiler avec flags de debug
compile_debug() {
    gcc -g -fno-stack-protector -z execstack -no-pie "$1" -o "${1%.*}"
}

# Fonction pour compiler vulnérable (pour tests)
compile_vuln() {
    gcc -g -fno-stack-protector -z execstack -no-pie -D_FORTIFY_SOURCE=0 "$1" -o "${1%.*}"
}

EOF

echo "✅ Configuration terminée !"
echo ""
echo "🎯 Prochaines étapes :"
echo "1. Redémarrer le terminal ou faire: source ~/.bashrc"
echo "2. Tester avec: python3 -c 'from pwn import *; print(\"pwntools OK!\")'"
echo "3. Tester GDB avec: gdb --version"
echo ""
echo "📚 Outils installés :"
echo "- pwntools (Python library pour exploitation)"
echo "- GDB avec GEF (debugger amélioré)"
echo "- radare2 (reverse engineering)"
echo "- Compilateurs avec support 32/64 bits"
echo "- Outils d'analyse binaire (objdump, readelf, strings...)"
echo ""
echo "🚀 Tu es maintenant prêt pour commencer le pwning !"
