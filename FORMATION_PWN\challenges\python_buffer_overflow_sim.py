#!/usr/bin/env python3
"""
CHALLENGE PYTHON: Simulation de Buffer Overflow
Formation PWN pour CTF

Ce challenge simule un buffer overflow en Python pour enseigner
les concepts fondamentaux sans avoir besoin de compiler du C.
"""

import struct
import sys
from pwn import *

class VulnerableProgram:
    """Simule un programme vulnérable avec buffer overflow"""
    
    def __init__(self):
        self.buffer_size = 64
        self.stack = bytearray(200)  # Simule la stack
        self.rbp_offset = 72  # Position de RBP sauvegardé
        self.rip_offset = 80  # Position de l'adresse de retour
        self.win_address = 0x401337  # Adresse fictive de win()
        self.main_address = 0x401000  # Adresse fictive de main()
        self.current_rip = self.main_address
        
        # Initialiser la stack avec des valeurs par défaut
        self.reset_stack()
    
    def reset_stack(self):
        """Remet la stack dans son état initial"""
        self.stack = bytearray(200)
        # Simuler RBP sauvegardé
        struct.pack_into('<Q', self.stack, self.rbp_offset, 0x7fff12345678)
        # Simuler adresse de retour normale
        struct.pack_into('<Q', self.stack, self.rip_offset, self.main_address)
    
    def show_info(self):
        """Affiche les informations du programme"""
        print("🎯 CHALLENGE PYTHON: Simulation Buffer Overflow")
        print("=" * 55)
        print(f"📊 Configuration:")
        print(f"   - Taille du buffer: {self.buffer_size} bytes")
        print(f"   - Offset RBP: {self.rbp_offset} bytes")
        print(f"   - Offset RIP: {self.rip_offset} bytes")
        print(f"   - Adresse de win(): {hex(self.win_address)}")
        print(f"   - Adresse de main(): {hex(self.main_address)}")
        print("=" * 55)
        print()
    
    def show_stack(self):
        """Affiche l'état actuel de la stack"""
        print("📊 État de la stack:")
        print("-" * 40)
        
        # Afficher les zones importantes
        print(f"Buffer (0-{self.buffer_size-1}):")
        buffer_data = self.stack[0:self.buffer_size]
        print(f"  {buffer_data.hex()}")
        
        print(f"\nPadding ({self.buffer_size}-{self.rbp_offset-1}):")
        padding_data = self.stack[self.buffer_size:self.rbp_offset]
        print(f"  {padding_data.hex()}")
        
        print(f"\nRBP sauvegardé ({self.rbp_offset}-{self.rbp_offset+7}):")
        rbp_data = self.stack[self.rbp_offset:self.rbp_offset+8]
        rbp_value = struct.unpack('<Q', rbp_data)[0]
        print(f"  {rbp_data.hex()} -> {hex(rbp_value)}")
        
        print(f"\nAdresse de retour (RIP) ({self.rip_offset}-{self.rip_offset+7}):")
        rip_data = self.stack[self.rip_offset:self.rip_offset+8]
        rip_value = struct.unpack('<Q', rip_data)[0]
        print(f"  {rip_data.hex()} -> {hex(rip_value)}")
        
        print("-" * 40)
    
    def vulnerable_function(self, user_input):
        """Simule une fonction vulnérable avec strcpy"""
        print("🔧 Exécution de vulnerable_function()")
        print(f"📝 Input reçu: {len(user_input)} bytes")
        
        # Simuler strcpy sans vérification de taille
        input_bytes = user_input.encode('latin-1') if isinstance(user_input, str) else user_input
        
        # Copier l'input dans la stack (simulation de strcpy)
        copy_length = min(len(input_bytes), len(self.stack))
        self.stack[0:copy_length] = input_bytes[0:copy_length]
        
        print(f"✅ Copie de {copy_length} bytes dans le buffer")
        
        # Vérifier si on a débordé
        if len(input_bytes) > self.buffer_size:
            print(f"⚠️ OVERFLOW DÉTECTÉ ! {len(input_bytes)} > {self.buffer_size}")
            
            if len(input_bytes) > self.rbp_offset:
                print("💥 RBP écrasé !")
                
            if len(input_bytes) > self.rip_offset:
                print("🎯 ADRESSE DE RETOUR ÉCRASÉE !")
                # Récupérer la nouvelle adresse de retour
                if len(input_bytes) >= self.rip_offset + 8:
                    new_rip = struct.unpack('<Q', self.stack[self.rip_offset:self.rip_offset+8])[0]
                    print(f"   Nouvelle RIP: {hex(new_rip)}")
                    
                    # Vérifier si c'est l'adresse de win()
                    if new_rip == self.win_address:
                        print("🎉 EXPLOITATION RÉUSSIE !")
                        return self.win()
                    else:
                        print("💀 CRASH ! Adresse invalide")
                        return False
        
        print("✅ Fonction terminée normalement")
        return True
    
    def win(self):
        """Fonction secrète à exécuter"""
        print("\n" + "🎉" * 20)
        print("🏆 FÉLICITATIONS ! Tu as réussi l'exploitation !")
        print("🎯 FLAG: CTF{python_buffer_overflow_simulation_mastered}")
        print("✅ Tu comprends maintenant les buffer overflows !")
        print("🎉" * 20 + "\n")
        return True
    
    def run_challenge(self):
        """Lance le challenge interactif"""
        self.show_info()
        
        while True:
            print("\n🎮 MENU:")
            print("1. Tester un input")
            print("2. Voir l'état de la stack")
            print("3. Générer un payload d'exploitation")
            print("4. Reset de la stack")
            print("5. Quitter")
            
            try:
                choice = input("\nChoix (1-5): ").strip()
            except KeyboardInterrupt:
                print("\n👋 Au revoir !")
                break
            
            if choice == '1':
                self.test_input()
            elif choice == '2':
                self.show_stack()
            elif choice == '3':
                self.generate_exploit()
            elif choice == '4':
                self.reset_stack()
                print("✅ Stack remise à zéro")
            elif choice == '5':
                print("👋 Au revoir !")
                break
            else:
                print("❌ Choix invalide")
    
    def test_input(self):
        """Teste un input utilisateur"""
        print("\n📝 Test d'input:")
        print("💡 Exemples:")
        print("   - Input normal: AAAA")
        print("   - Overflow simple: " + "A" * 70)
        print("   - Exploitation: " + "A" * 80 + "[adresse]")
        
        try:
            user_input = input("Entre ton input: ")
            self.vulnerable_function(user_input)
            self.show_stack()
        except KeyboardInterrupt:
            print("\n⏸️ Annulé")
    
    def generate_exploit(self):
        """Génère un payload d'exploitation"""
        print("\n🔧 GÉNÉRATION D'EXPLOIT:")
        print("=" * 40)
        
        # Construire le payload
        padding = b'A' * self.rip_offset
        win_addr_bytes = struct.pack('<Q', self.win_address)
        payload = padding + win_addr_bytes
        
        print(f"📊 Payload construit:")
        print(f"   - Padding: {len(padding)} bytes ('A')")
        print(f"   - Adresse win(): {hex(self.win_address)}")
        print(f"   - Taille totale: {len(payload)} bytes")
        
        print(f"\n🔍 Payload en hexadécimal:")
        print(f"   {payload.hex()}")
        
        print(f"\n🐍 Code Python:")
        print(f"   payload = b'A' * {self.rip_offset} + struct.pack('<Q', {hex(self.win_address)})")
        
        # Tester le payload
        test = input("\n🧪 Tester ce payload ? (y/n): ").strip().lower()
        if test == 'y':
            print("\n🚀 Test du payload...")
            self.reset_stack()
            self.vulnerable_function(payload)

def main():
    """Fonction principale"""
    print("🎯 FORMATION PWN - SIMULATION BUFFER OVERFLOW")
    print("=" * 60)
    print("Challenge Python pour apprendre les concepts sans compilation")
    print("=" * 60)
    
    # Créer et lancer le challenge
    challenge = VulnerableProgram()
    challenge.run_challenge()

if __name__ == "__main__":
    main()

"""
NOTES PÉDAGOGIQUES:

1. CONCEPTS ENSEIGNÉS:
   - Structure de la stack
   - Buffer overflow
   - Contrôle de RIP
   - Construction de payload
   - Little-endian encoding

2. AVANTAGES DE LA SIMULATION:
   - Pas besoin de compilateur
   - Visualisation claire de la stack
   - Contrôle total sur l'environnement
   - Apprentissage progressif

3. CORRESPONDANCE AVEC LA RÉALITÉ:
   - Même logique d'exploitation
   - Mêmes offsets et calculs
   - Même construction de payload
   - Concepts transférables aux vrais binaires

4. PROCHAINES ÉTAPES:
   - Maîtriser cette simulation
   - Installer un compilateur (MinGW/WSL)
   - Passer aux vrais binaires
   - Apprendre GDB et l'analyse statique
"""
