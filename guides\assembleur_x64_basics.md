# 🔧 Fondamentaux de l'Assembleur x86/x64 pour PWN

## 📋 Registres Essentiels

### Registres 64-bit (x64)
```
RAX - Accumulateur (valeur de retour)
RBX - Base (usage général)
RCX - Compteur (4ème argument)
RDX - Données (3ème argument)
RSI - Source Index (2ème argument)
RDI - Destination Index (1er argument)
RSP - Stack Pointer (pointeur de pile)
RBP - Base Pointer (base de frame)
RIP - Instruction Pointer (adresse instruction courante)
R8-R15 - Registres supplémentaires
```

### Registres 32-bit (x86)
```
EAX, EBX, EXX, EDX, ESI, EDI, ESP, EBP, EIP
```

### Registres 16-bit et 8-bit
```
AX (16-bit) = AH (8-bit haut) + AL (8-bit bas)
```

## 🏗️ Structure de la Stack

```
Adresses hautes
+------------------+
| Arguments        |
+------------------+
| Adresse retour   | <- RIP sauvegardé
+------------------+
| RBP sauvegardé   | <- RBP pointe ici
+------------------+
| Variables locales| <- RSP pointe ici
+------------------+
Adresses basses
```

## 📞 Calling Convention (System V ABI - Linux)

### Ordre des arguments (64-bit)
1. RDI (1er argument)
2. RSI (2ème argument)  
3. RDX (3ème argument)
4. RCX (4ème argument)
5. R8 (5ème argument)
6. R9 (6ème argument)
7. Stack (arguments suivants)

### Exemple d'appel de fonction
```c
int func(int a, int b, int c, int d, int e, int f, int g);
func(1, 2, 3, 4, 5, 6, 7);
```

```asm
mov rdi, 1    ; 1er argument
mov rsi, 2    ; 2ème argument  
mov rdx, 3    ; 3ème argument
mov rcx, 4    ; 4ème argument
mov r8, 5     ; 5ème argument
mov r9, 6     ; 6ème argument
push 7        ; 7ème argument sur la stack
call func
```

## 🔧 Instructions Essentielles

### Mouvement de données
```asm
mov rax, rbx     ; rax = rbx
mov rax, 0x1234  ; rax = 0x1234
lea rax, [rbx+8] ; rax = adresse de rbx+8
```

### Opérations arithmétiques
```asm
add rax, rbx     ; rax = rax + rbx
sub rax, rbx     ; rax = rax - rbx
mul rbx          ; rax = rax * rbx
div rbx          ; rax = rax / rbx
```

### Manipulation de la stack
```asm
push rax         ; Empile rax, RSP -= 8
pop rax          ; Dépile dans rax, RSP += 8
```

### Contrôle de flux
```asm
jmp label        ; Saut inconditionnel
je label         ; Saut si égal
jne label        ; Saut si différent
call func        ; Appel de fonction
ret              ; Retour de fonction
```

### Comparaisons
```asm
cmp rax, rbx     ; Compare rax et rbx
test rax, rax    ; Test si rax == 0
```

## 🎯 Exemple Pratique : Fonction Simple

### Code C
```c
int add(int a, int b) {
    return a + b;
}

int main() {
    int result = add(5, 3);
    return result;
}
```

### Assembleur généré (simplifié)
```asm
add:
    push rbp          ; Sauvegarde RBP
    mov rbp, rsp      ; Nouveau frame
    mov eax, edi      ; a (1er arg) dans EAX
    add eax, esi      ; a + b (2ème arg)
    pop rbp           ; Restaure RBP
    ret               ; Retour avec résultat dans EAX

main:
    push rbp          ; Sauvegarde RBP
    mov rbp, rsp      ; Nouveau frame
    mov edi, 5        ; 1er argument = 5
    mov esi, 3        ; 2ème argument = 3
    call add          ; Appel de add()
    pop rbp           ; Restaure RBP
    ret               ; Retour
```

## 🔍 Analyse avec GDB

### Commandes essentielles
```bash
# Lancement
gdb ./programme

# Points d'arrêt
break main
break *0x401000

# Exécution
run
continue
step (si)
next (ni)

# Examen des registres
info registers
print $rax
print $rsp

# Examen de la mémoire
x/10gx $rsp        # 10 mots de 8 bytes en hexa
x/10i $rip         # 10 instructions
x/s 0x401000       # String à l'adresse

# Stack
bt                 # Backtrace
frame 0            # Frame courante
```

## 🎮 Exercice Pratique

Créons un programme simple pour analyser :

```c
// simple.c
#include <stdio.h>

void vulnerable(char *input) {
    char buffer[64];
    strcpy(buffer, input);  // VULNÉRABLE !
    printf("Buffer: %s\n", buffer);
}

int main(int argc, char *argv[]) {
    if (argc != 2) {
        printf("Usage: %s <input>\n", argv[0]);
        return 1;
    }
    vulnerable(argv[1]);
    return 0;
}
```

### Compilation et analyse
```bash
# Compilation avec symboles de debug
gcc -g -fno-stack-protector -z execstack simple.c -o simple

# Analyse avec objdump
objdump -d simple | grep -A 20 "<vulnerable>"

# Test avec GDB
gdb ./simple
break vulnerable
run AAAA
disas vulnerable
x/20gx $rsp
```

## 🚀 Prochaines Étapes

1. **Pratique** : Compile et analyse le programme simple
2. **GDB** : Maîtrise les commandes de base
3. **Stack** : Comprends la structure de la pile
4. **Overflow** : Identifie comment déborder le buffer

## 💡 Points Clés à Retenir

- **RSP** pointe toujours vers le sommet de la stack
- **RBP** sert de référence pour les variables locales
- **RIP** contient l'adresse de la prochaine instruction
- Les **arguments** sont passés dans les registres (Linux x64)
- La **valeur de retour** est dans RAX
- La stack **grandit vers le bas** (adresses décroissantes)

## 🔗 Ressources Supplémentaires

- [Intel x64 Manual](https://software.intel.com/content/www/us/en/develop/articles/intel-sdm.html)
- [System V ABI](https://refspecs.linuxbase.org/elf/x86_64-abi-0.99.pdf)
- [GDB Cheat Sheet](https://darkdust.net/files/GDB%20Cheat%20Sheet.pdf)

Maintenant, passons à la pratique avec ton premier challenge ! 🎯
