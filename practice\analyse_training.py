#!/usr/bin/env python3
"""
Entraînement à l'Analyse de Binaires
Formation PWN pour CTF

Ce script simule l'utilisation des outils d'analyse statique et dynamique
pour enseigner la méthodologie d'analyse de binaires.
"""

from pwn import *
import random
import struct

class BinaryAnalysisTraining:
    """Simulateur d'analyse de binaires pour l'entraînement"""
    
    def __init__(self):
        self.binary_info = self.generate_sample_binary()
        
    def generate_sample_binary(self):
        """Génère les informations d'un binaire d'exemple"""
        return {
            'name': 'challenge_example',
            'arch': 'amd64',
            'bits': 64,
            'endian': 'little',
            'type': 'ELF executable',
            'protections': {
                'nx': random.choice([True, False]),
                'pie': random.choice([True, False]),
                'canary': random.choice([True, False]),
                'relro': random.choice(['No RELRO', 'Partial RELRO', 'Full RELRO'])
            },
            'functions': {
                'main': 0x401136,
                'vulnerable': 0x401200,
                'win': 0x401337,
                'gets': 0x401040,  # PLT
                'printf': 0x401050  # PLT
            },
            'strings': [
                "Enter your input:",
                "You entered: %s",
                "Buffer overflow detected!",
                "Congratulations!",
                "/bin/sh",
                "flag{example_flag}"
            ],
            'vulnerabilities': ['buffer_overflow', 'format_string'],
            'buffer_size': 64
        }
    
    def show_file_analysis(self):
        """Simule la commande 'file'"""
        print("🔍 ANALYSE AVEC 'file'")
        print("=" * 40)
        info = self.binary_info
        print(f"{info['name']}: {info['type']}, {info['arch']}-{info['bits']}, {info['endian']} endian")
        print()
    
    def show_checksec_analysis(self):
        """Simule la commande 'checksec'"""
        print("🛡️ ANALYSE DES PROTECTIONS (checksec)")
        print("=" * 40)
        prot = self.binary_info['protections']
        
        print(f"[*] '{self.binary_info['name']}'")
        print(f"    Arch:     {self.binary_info['arch']}-{self.binary_info['bits']}-{self.binary_info['endian']}")
        print(f"    RELRO:    {prot['relro']}")
        print(f"    Stack:    {'Canary found' if prot['canary'] else 'No canary found'}")
        print(f"    NX:       {'NX enabled' if prot['nx'] else 'NX disabled'}")
        print(f"    PIE:      {'PIE enabled' if prot['pie'] else 'No PIE'}")
        
        print("\n💡 Implications pour l'exploitation:")
        if not prot['canary']:
            print("   ✅ Pas de canary -> Buffer overflow plus facile")
        if not prot['nx']:
            print("   ✅ NX désactivé -> Shellcode possible")
        if not prot['pie']:
            print("   ✅ Pas de PIE -> Adresses fixes, pas de leak nécessaire")
        print()
    
    def show_strings_analysis(self):
        """Simule la commande 'strings'"""
        print("📝 ANALYSE DES STRINGS")
        print("=" * 40)
        
        print("Strings intéressantes trouvées:")
        for i, string in enumerate(self.binary_info['strings'], 1):
            print(f"  {i:2}. {string}")
        
        print("\n💡 Analyse:")
        strings = self.binary_info['strings']
        if any('flag' in s.lower() for s in strings):
            print("   🎯 Flag potentiel détecté !")
        if any('/bin/sh' in s for s in strings):
            print("   🐚 Shell string trouvé -> system() possible")
        if any('%s' in s for s in strings):
            print("   ⚠️ Format string détecté -> Vulnérabilité possible")
        print()
    
    def show_objdump_analysis(self):
        """Simule l'analyse avec objdump"""
        print("🔧 ANALYSE AVEC objdump")
        print("=" * 40)
        
        functions = self.binary_info['functions']
        
        print("Fonctions importantes identifiées:")
        for func, addr in functions.items():
            print(f"  {func:12} @ {hex(addr)}")
        
        print(f"\nDésassemblage de la fonction vulnerable:")
        print(f"0000000000401200 <vulnerable>:")
        print(f"  401200: push   %rbp")
        print(f"  401201: mov    %rsp,%rbp")
        print(f"  401204: sub    $0x50,%rsp        # {self.binary_info['buffer_size']+16} bytes d'espace")
        print(f"  401208: lea    -0x40(%rbp),%rax  # buffer à RBP-{hex(self.binary_info['buffer_size'])}")
        print(f"  40120c: mov    %rax,%rdi")
        print(f"  40120f: call   401040 <gets@plt> # VULNÉRABLE !")
        print(f"  401214: leave")
        print(f"  401215: ret")
        
        print("\n💡 Analyse du code:")
        print(f"   - Buffer de {self.binary_info['buffer_size']} bytes alloué")
        print("   - Utilisation de gets() -> Buffer overflow possible")
        print("   - Pas de vérification de taille")
        print()
    
    def show_gdb_simulation(self):
        """Simule une session GDB"""
        print("🐛 SIMULATION GDB")
        print("=" * 40)
        
        print("(gdb) break vulnerable")
        print("Breakpoint 1 at 0x401200")
        print()
        print("(gdb) run")
        print("Starting program: ./challenge_example")
        print("Breakpoint 1, 0x0000000000401200 in vulnerable ()")
        print()
        print("(gdb) disas vulnerable")
        print("Dump of assembler code for function vulnerable:")
        print("=> 0x0000000000401200 <+0>:     push   %rbp")
        print("   0x0000000000401201 <+1>:     mov    %rsp,%rbp")
        print("   0x0000000000401204 <+4>:     sub    $0x50,%rsp")
        print("   0x0000000000401208 <+8>:     lea    -0x40(%rbp),%rax")
        print("   0x000000000040120c <+12>:    mov    %rax,%rdi")
        print("   0x000000000040120f <+15>:    call   0x401040 <gets@plt>")
        print("   0x0000000000401214 <+20>:    leave")
        print("   0x0000000000401215 <+21>:    ret")
        print("End of assembler dump.")
        print()
        print("(gdb) x/10gx $rsp")
        print("0x7fffffffe3a0: 0x00007fffffffe3f0  0x0000000000401136")
        print("0x7fffffffe3b0: 0x0000000000000000  0x0000000000000000")
        print("0x7fffffffe3c0: 0x0000000000000000  0x0000000000000000")
        print("0x7fffffffe3d0: 0x0000000000000000  0x0000000000000000")
        print("0x7fffffffe3e0: 0x0000000000000000  0x0000000000000000")
        print()
    
    def calculate_exploit_offset(self):
        """Calcule l'offset pour l'exploitation"""
        print("🎯 CALCUL DE L'OFFSET D'EXPLOITATION")
        print("=" * 40)
        
        buffer_size = self.binary_info['buffer_size']
        
        print("Analyse de la stack frame:")
        print(f"  Buffer:           {buffer_size} bytes")
        print(f"  Padding:          8 bytes (alignement)")
        print(f"  RBP sauvegardé:   8 bytes")
        print(f"  Adresse retour:   8 bytes")
        print()
        
        offset = buffer_size + 8  # buffer + padding pour atteindre RBP
        rip_offset = offset + 8   # + RBP pour atteindre RIP
        
        print(f"Offsets calculés:")
        print(f"  Offset RBP:  {offset} bytes")
        print(f"  Offset RIP:  {rip_offset} bytes")
        print()
        
        print("🔧 Construction du payload:")
        win_addr = self.binary_info['functions']['win']
        print(f"  payload = b'A' * {rip_offset} + p64({hex(win_addr)})")
        
        # Générer le payload
        payload = b'A' * rip_offset + struct.pack('<Q', win_addr)
        print(f"  Taille totale: {len(payload)} bytes")
        print(f"  Payload hex: {payload.hex()}")
        print()
        
        return rip_offset, win_addr
    
    def vulnerability_assessment(self):
        """Évalue les vulnérabilités trouvées"""
        print("⚠️ ÉVALUATION DES VULNÉRABILITÉS")
        print("=" * 40)
        
        vulns = self.binary_info['vulnerabilities']
        prot = self.binary_info['protections']
        
        print("Vulnérabilités identifiées:")
        for vuln in vulns:
            if vuln == 'buffer_overflow':
                print("  🔴 Buffer Overflow (gets() sans vérification)")
                print("     Impact: Contrôle de l'exécution")
                print("     Exploitabilité: Élevée")
            elif vuln == 'format_string':
                print("  🟡 Format String (printf avec input utilisateur)")
                print("     Impact: Leak d'informations, écriture arbitraire")
                print("     Exploitabilité: Moyenne")
        
        print("\nFacteurs facilitant l'exploitation:")
        if not prot['canary']:
            print("  ✅ Pas de stack canary")
        if not prot['nx']:
            print("  ✅ Stack exécutable")
        if not prot['pie']:
            print("  ✅ Adresses fixes")
        
        print("\nRecommandation d'exploitation:")
        if 'buffer_overflow' in vulns and not prot['canary']:
            print("  🎯 Buffer overflow -> ret2win recommandé")
        print()
    
    def interactive_training(self):
        """Session d'entraînement interactive"""
        print("🎓 ENTRAÎNEMENT ANALYSE DE BINAIRES")
        print("=" * 50)
        print("Simulation d'analyse d'un binaire CTF")
        print("=" * 50)
        print()
        
        while True:
            print("🔍 MENU D'ANALYSE:")
            print("1. Analyse de base (file)")
            print("2. Protections (checksec)")
            print("3. Strings intéressantes")
            print("4. Désassemblage (objdump)")
            print("5. Simulation GDB")
            print("6. Calcul d'offset d'exploitation")
            print("7. Évaluation des vulnérabilités")
            print("8. Générer un nouveau binaire")
            print("9. Quitter")
            
            try:
                choice = input("\nChoix (1-9): ").strip()
            except KeyboardInterrupt:
                print("\n👋 Au revoir !")
                break
            
            print()
            if choice == '1':
                self.show_file_analysis()
            elif choice == '2':
                self.show_checksec_analysis()
            elif choice == '3':
                self.show_strings_analysis()
            elif choice == '4':
                self.show_objdump_analysis()
            elif choice == '5':
                self.show_gdb_simulation()
            elif choice == '6':
                self.calculate_exploit_offset()
            elif choice == '7':
                self.vulnerability_assessment()
            elif choice == '8':
                self.binary_info = self.generate_sample_binary()
                print("✅ Nouveau binaire généré !")
            elif choice == '9':
                print("👋 Au revoir !")
                break
            else:
                print("❌ Choix invalide")
            
            input("\nAppuie sur Entrée pour continuer...")
            print()

def main():
    """Fonction principale"""
    trainer = BinaryAnalysisTraining()
    trainer.interactive_training()

if __name__ == "__main__":
    main()

"""
OBJECTIFS PÉDAGOGIQUES:

1. MÉTHODOLOGIE D'ANALYSE:
   - Approche systématique
   - Outils appropriés pour chaque étape
   - Interprétation des résultats

2. IDENTIFICATION DES VULNÉRABILITÉS:
   - Reconnaissance des patterns dangereux
   - Évaluation de l'exploitabilité
   - Impact des protections

3. PRÉPARATION DE L'EXPLOITATION:
   - Calcul des offsets
   - Construction de payloads
   - Planification de l'attaque

4. COMPÉTENCES TRANSFÉRABLES:
   - Concepts applicables aux vrais binaires
   - Méthodologie professionnelle
   - Pensée critique en sécurité
"""
