#!/usr/bin/env python3
"""
CHALLENGE OSINT 002: Géolocalisation
Niveau: Intermédiaire
Objectif: Maîtriser les techniques de géolocalisation OSINT

Ce challenge enseigne l'identification de lieux à partir d'images,
l'analyse de métadonnées et les techniques de GEOINT.
"""

import os
import json
import base64
from datetime import datetime
import random

class OSINTChallenge002:
    """Challenge OSINT de géolocalisation"""
    
    def __init__(self):
        self.score = 0
        self.max_score = 100
        self.current_challenge = 0
        
        # Données d'exemple pour les challenges
        self.locations = {
            'paris_tower': {
                'name': 'Tour Eiffel, Paris',
                'coordinates': '48.8584, 2.2945',
                'country': 'France',
                'city': 'Paris',
                'hints': [
                    'Structure métallique emblématique',
                    'Construite pour l\'Exposition universelle de 1889',
                    'Hauteur: 330 mètres',
                    'Architecte: <PERSON><PERSON>'
                ]
            },
            'london_bridge': {
                'name': 'Tower Bridge, Londres',
                'coordinates': '51.5055, -0.0754',
                'country': 'Royaume-Uni',
                'city': 'Londres',
                'hints': [
                    'Pont basculant victorien',
                    'Construit entre 1886 et 1894',
                    'Traverse la Tamise',
                    'Tours de style gothique'
                ]
            },
            'ny_statue': {
                'name': 'Statue de la Liberté, New York',
                'coordinates': '40.6892, -74.0445',
                'country': 'États-Unis',
                'city': 'New York',
                'hints': [
                    'Cadeau de la France aux États-Unis',
                    'Inaugurée en 1886',
                    'Située sur Liberty Island',
                    'Symbole de liberté et démocratie'
                ]
            }
        }
        
        # Métadonnées d'exemple
        self.sample_metadata = {
            'camera': 'Canon EOS 5D Mark IV',
            'gps_lat': '48.8584',
            'gps_lon': '2.2945',
            'date_taken': '2023-07-15 14:30:22',
            'altitude': '35m',
            'direction': 'Nord-Est'
        }
    
    def show_intro(self):
        """Affiche l'introduction du challenge"""
        print("🌍 CHALLENGE OSINT 002: GÉOLOCALISATION")
        print("=" * 55)
        print("🎯 Objectif: Maîtriser les techniques de géolocalisation")
        print("📊 Niveau: Intermédiaire")
        print("⏱️ Durée estimée: 45-60 minutes")
        print("🏆 Score maximum: 100 points")
        print()
        print("📚 Ce que vous allez apprendre:")
        print("  • Analyse d'images pour géolocalisation")
        print("  • Extraction et analyse de métadonnées")
        print("  • Utilisation de Google Earth/Maps")
        print("  • Techniques de recherche inversée d'images")
        print("  • Corrélation d'indices visuels")
        print()
        print("🛠️ Outils recommandés:")
        print("  • Google Images (recherche inversée)")
        print("  • TinEye")
        print("  • Google Earth/Maps")
        print("  • Exiftool ou analyseur de métadonnées")
        print()
        print("=" * 55)
        print()
    
    def challenge_1_metadata_analysis(self):
        """Challenge 1: Analyse de métadonnées"""
        print("📊 CHALLENGE 1: ANALYSE DE MÉTADONNÉES (25 points)")
        print("-" * 50)
        print("Vous avez récupéré les métadonnées suivantes d'une image:")
        print()
        
        # Afficher les métadonnées
        print("🔍 MÉTADONNÉES EXTRAITES:")
        for key, value in self.sample_metadata.items():
            print(f"  {key.replace('_', ' ').title()}: {value}")
        print()
        
        questions = [
            {
                'question': 'Quelles sont les coordonnées GPS de cette image ?',
                'answer': '48.8584, 2.2945',
                'points': 10,
                'flexible': True
            },
            {
                'question': 'Dans quelle ville cette photo a-t-elle probablement été prise ?',
                'answer': 'Paris',
                'points': 10
            },
            {
                'question': 'Quel outil en ligne pourriez-vous utiliser pour extraire ces métadonnées ?',
                'answer': 'exiftool',
                'points': 5,
                'flexible': True
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"Question {i} ({q['points']} points):")
            print(q['question'])
            
            user_answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                if any(word in user_answer.lower() for word in q['answer'].lower().split()):
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            else:
                if user_answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            print()
        
        self.score += score
        print(f"📊 Score Challenge 1: {score}/25 points")
        return score
    
    def challenge_2_landmark_identification(self):
        """Challenge 2: Identification de monuments"""
        print("\n🏛️ CHALLENGE 2: IDENTIFICATION DE MONUMENTS (25 points)")
        print("-" * 50)
        print("Identifiez les lieux à partir des descriptions suivantes:")
        print()
        
        # Sélectionner un lieu aléatoire
        location_key = random.choice(list(self.locations.keys()))
        location = self.locations[location_key]
        
        print("🔍 INDICES VISUELS:")
        for i, hint in enumerate(location['hints'], 1):
            print(f"  {i}. {hint}")
        print()
        
        questions = [
            {
                'question': 'Quel est le nom de ce monument/lieu ?',
                'answer': location['name'].split(',')[0],
                'points': 10,
                'flexible': True
            },
            {
                'question': 'Dans quelle ville se trouve-t-il ?',
                'answer': location['city'],
                'points': 8
            },
            {
                'question': 'Dans quel pays ?',
                'answer': location['country'],
                'points': 7
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"Question {i} ({q['points']} points):")
            print(q['question'])
            
            user_answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                if any(word in user_answer.lower() for word in q['answer'].lower().split()):
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            else:
                if user_answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            print()
        
        self.score += score
        print(f"📊 Score Challenge 2: {score}/25 points")
        return score
    
    def challenge_3_reverse_image_search(self):
        """Challenge 3: Recherche inversée d'images"""
        print("\n🔍 CHALLENGE 3: RECHERCHE INVERSÉE D'IMAGES (25 points)")
        print("-" * 50)
        print("Vous devez utiliser la recherche inversée d'images pour identifier un lieu.")
        print()
        
        print("📝 SCÉNARIO:")
        print("Vous avez une image d'un bâtiment avec les caractéristiques suivantes:")
        print("  • Architecture gothique")
        print("  • Grande rosace circulaire")
        print("  • Deux tours carrées")
        print("  • Située sur une île")
        print("  • Cathédrale célèbre")
        print()
        
        questions = [
            {
                'question': 'Quels outils utiliseriez-vous pour la recherche inversée ? (citez-en 2)',
                'answer': 'Google Images, TinEye',
                'points': 8,
                'flexible': True
            },
            {
                'question': 'Basé sur les indices, quel monument s\'agit-il probablement ?',
                'answer': 'Notre-Dame',
                'points': 10,
                'flexible': True
            },
            {
                'question': 'Comment vérifieriez-vous votre identification ?',
                'answer': 'Google Earth, Wikipedia',
                'points': 7,
                'flexible': True
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"Question {i} ({q['points']} points):")
            print(q['question'])
            
            user_answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                answer_words = q['answer'].lower().split(', ')
                user_lower = user_answer.lower()
                matches = sum(1 for word in answer_words if word in user_lower)
                
                if matches >= 1:
                    points_earned = int(q['points'] * (matches / len(answer_words)))
                    print(f"✅ Partiellement correct ! +{points_earned} points")
                    score += points_earned
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            else:
                if user_answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            print()
        
        self.score += score
        print(f"📊 Score Challenge 3: {score}/25 points")
        return score
    
    def challenge_4_geoint_analysis(self):
        """Challenge 4: Analyse GEOINT avancée"""
        print("\n🛰️ CHALLENGE 4: ANALYSE GEOINT AVANCÉE (25 points)")
        print("-" * 50)
        print("Analyse avancée de géolocalisation avec corrélation d'indices.")
        print()
        
        print("📝 SCÉNARIO COMPLEXE:")
        print("Une photo montre:")
        print("  • Un café avec terrasse")
        print("  • Une rue pavée")
        print("  • Un panneau 'Rue de Rivoli'")
        print("  • Vue sur un grand musée avec pyramide de verre")
        print("  • Métadonnées: 48.8606, 2.3376")
        print()
        
        questions = [
            {
                'question': 'Dans quelle ville cette photo a-t-elle été prise ?',
                'answer': 'Paris',
                'points': 5
            },
            {
                'question': 'Quel est le musée visible avec la pyramide de verre ?',
                'answer': 'Louvre',
                'points': 8,
                'flexible': True
            },
            {
                'question': 'Comment pourriez-vous confirmer l\'emplacement exact du café ?',
                'answer': 'Google Street View',
                'points': 7,
                'flexible': True
            },
            {
                'question': 'Quelle technique avez-vous utilisée pour résoudre ce challenge ?',
                'answer': 'corrélation d\'indices',
                'points': 5,
                'flexible': True
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"Question {i} ({q['points']} points):")
            print(q['question'])
            
            user_answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                if any(word in user_answer.lower() for word in q['answer'].lower().split()):
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            else:
                if user_answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            print()
        
        self.score += score
        print(f"📊 Score Challenge 4: {score}/25 points")
        return score
    
    def show_geolocation_tips(self):
        """Affiche des conseils de géolocalisation"""
        print("\n💡 CONSEILS DE GÉOLOCALISATION OSINT")
        print("=" * 50)
        print("🔍 INDICES À RECHERCHER:")
        print("  • Panneaux de signalisation")
        print("  • Architecture caractéristique")
        print("  • Végétation et climat")
        print("  • Plaques d'immatriculation")
        print("  • Enseignes et publicités")
        print("  • Monuments et points de repère")
        print()
        print("🛠️ OUTILS ESSENTIELS:")
        print("  • Google Earth/Maps")
        print("  • Google Street View")
        print("  • TinEye (recherche inversée)")
        print("  • Wikimapia")
        print("  • Exiftool (métadonnées)")
        print()
        print("📋 MÉTHODOLOGIE:")
        print("  1. Extraire les métadonnées")
        print("  2. Identifier les indices visuels")
        print("  3. Recherche inversée d'images")
        print("  4. Corrélation avec cartes")
        print("  5. Vérification finale")
        print()
    
    def show_final_results(self):
        """Affiche les résultats finaux"""
        print("\n🏆 RÉSULTATS FINAUX - GÉOLOCALISATION")
        print("=" * 50)
        print(f"Score total: {self.score}/{self.max_score} points")
        print(f"Pourcentage: {(self.score/self.max_score)*100:.1f}%")
        print()
        
        if self.score >= 90:
            print("🥇 EXPERT EN GÉOLOCALISATION !")
            print("🌍 Vous maîtrisez parfaitement le GEOINT.")
            print("🚀 Prêt pour les challenges CTF de géolocalisation.")
        elif self.score >= 70:
            print("🥈 BONNES COMPÉTENCES EN GÉOLOCALISATION !")
            print("🗺️ Quelques techniques à perfectionner.")
            print("📚 Pratiquez avec Google Earth et Street View.")
        elif self.score >= 50:
            print("🥉 BASES ACQUISES.")
            print("🔄 Travaillez l'analyse d'indices visuels.")
            print("🛠️ Familiarisez-vous avec les outils GEOINT.")
        else:
            print("📚 FORMATION NÉCESSAIRE.")
            print("🔄 Étudiez les guides de géolocalisation.")
            print("🎯 Pratiquez avec des exemples simples.")
        
        print("\n🎯 PROCHAINES ÉTAPES:")
        if self.score >= 70:
            print("  • Challenge 003: Réseaux sociaux")
            print("  • Challenge 004: OSINT technique")
            print("  • Challenges CTF réels")
        else:
            print("  • Pratiquer avec Google Earth")
            print("  • Analyser des métadonnées d'images")
            print("  • Refaire ce challenge")
        
        print("\n🌍 RESSOURCES GEOINT:")
        print("  • Google Earth Pro (gratuit)")
        print("  • Wikimapia.org")
        print("  • OpenStreetMap.org")
        print("  • SunCalc.org (position soleil)")
    
    def run_challenge(self):
        """Lance le challenge complet"""
        self.show_intro()
        
        input("Appuyez sur Entrée pour commencer...")
        print()
        
        # Lancer les 4 challenges
        self.challenge_1_metadata_analysis()
        self.challenge_2_landmark_identification()
        self.challenge_3_reverse_image_search()
        self.challenge_4_geoint_analysis()
        
        # Conseils et résultats
        self.show_geolocation_tips()
        self.show_final_results()

def main():
    """Fonction principale"""
    challenge = OSINTChallenge002()
    challenge.run_challenge()

if __name__ == "__main__":
    main()

"""
COMPÉTENCES DÉVELOPPÉES:

1. ANALYSE DE MÉTADONNÉES:
   - Extraction EXIF
   - Géolocalisation GPS
   - Informations temporelles

2. IDENTIFICATION VISUELLE:
   - Reconnaissance de monuments
   - Analyse architecturale
   - Indices culturels

3. RECHERCHE INVERSÉE:
   - Google Images
   - TinEye
   - Corrélation de résultats

4. GEOINT AVANCÉ:
   - Corrélation d'indices
   - Vérification croisée
   - Analyse contextuelle

Ce challenge prépare aux défis de géolocalisation
des CTF et aux investigations GEOINT réelles.
"""
