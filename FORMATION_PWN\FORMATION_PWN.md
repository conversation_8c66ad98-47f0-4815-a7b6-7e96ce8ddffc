# 🎯 Formation PWN pour CTF - Guide Complet

## 📋 Table des Matières

1. [Configuration de l'environnement](#configuration)
2. [Fondamentaux](#fondamentaux)
3. [Challenges progressifs](#challenges)
4. [Outils essentiels](#outils)
5. [Ressources](#ressources)

## 🚀 Configuration de l'environnement {#configuration}

### Étape 1: Installation des outils
```bash
# Dans WSL Ubuntu
chmod +x setup_pwn_env.sh
./setup_pwn_env.sh
```

### Étape 2: Vérification
```bash
# Test pwntools
python3 -c "from pwn import *; print('pwntools OK!')"

# Test GDB avec GEF
gdb --version
```

## 📚 Fondamentaux {#fondamentaux}

### 1. Architecture x86/x64
- **Registres importants**: RAX, RBX, RCX, RDX, RSI, RDI, RSP, RBP, RIP
- **Calling conventions**: System V ABI (Linux)
- **Stack layout**: Adresses hautes vers basses

### 2. Vulnérabilités communes
- **Buffer Overflow**: Dépassement de buffer
- **Format String**: Exploitation de printf/scanf
- **Use-After-Free**: Utilisation après libération
- **Integer Overflow**: Dépassement d'entier

### 3. Protections modernes
- **NX/DEP**: Pile non-exécutable
- **ASLR**: Randomisation des adresses
- **Stack Canaries**: Protection de la pile
- **PIE**: Position Independent Executable

## 🎮 Challenges Progressifs {#challenges}

### Niveau 1: Buffer Overflow Basique
- **Fichier**: `challenges/level1_basic_overflow.c`
- **Objectif**: Contrôler RIP pour exécuter win()
- **Protections**: Aucune
- **Concepts**: Stack, offset, payload construction

```bash
# Compilation
cd challenges
gcc -g -fno-stack-protector -z execstack -no-pie level1_basic_overflow.c -o level1

# Exploitation
cd ..
python3 exploits/level1_exploit.py
```

### Niveau 2: Buffer Overflow avec NX (à venir)
- **Objectif**: ROP chains, ret2libc
- **Protections**: NX activé
- **Concepts**: Gadgets, ROP, libc functions

### Niveau 3: ASLR Bypass (à venir)
- **Objectif**: Information leaks, partial overwrites
- **Protections**: NX + ASLR
- **Concepts**: Leaks, bruteforce, partial overwrites

## 🛠️ Outils Essentiels {#outils}

### Analyse Statique
```bash
file binary          # Type de fichier
checksec binary      # Protections activées
objdump -d binary    # Désassemblage
readelf -a binary    # Headers ELF
strings binary       # Chaînes de caractères
```

### Analyse Dynamique
```bash
gdb binary           # Debugger avec GEF
strace binary        # System calls
ltrace binary        # Library calls
```

### Pwntools (Python)
```python
from pwn import *

# Connexion
p = process('./binary')     # Local
p = remote('host', port)    # Remote

# Manipulation de données
payload = b'A' * 64 + p64(address)  # 64-bit
payload = b'A' * 64 + p32(address)  # 32-bit

# Pattern pour offset
pattern = cyclic(200)
offset = cyclic_find('pattern')

# Interaction
p.sendline(payload)
p.recvuntil(b'prompt: ')
p.interactive()
```

## 📖 Méthodologie d'Exploitation

### 1. Reconnaissance
- Analyser le binaire (file, checksec)
- Identifier les fonctions (objdump, ghidra)
- Chercher les vulnérabilités (code review)

### 2. Exploitation
- Calculer les offsets (pattern, gdb)
- Construire le payload
- Tester localement
- Adapter pour le remote

### 3. Post-Exploitation
- Obtenir un shell
- Escalader les privilèges
- Récupérer le flag

## 🎯 Plan de Formation (8 semaines)

### Semaine 1-2: Bases
- [x] Configuration environnement
- [ ] Assembleur x86/x64
- [ ] GDB et debugging
- [ ] Premier buffer overflow

### Semaine 3-4: Exploitation Basique
- [ ] Buffer overflows avancés
- [ ] Shellcoding
- [ ] Format string bugs
- [ ] Integer overflows

### Semaine 5-6: Protections et Bypass
- [ ] ROP chains
- [ ] ret2libc
- [ ] ASLR bypass
- [ ] Stack canary bypass

### Semaine 7-8: Exploitation Avancée
- [ ] Heap exploitation
- [ ] Kernel exploitation
- [ ] CTF challenges réels
- [ ] Développement d'exploits robustes

## 📚 Ressources Recommandées

### Livres
- "The Shellcoder's Handbook" - Koziol et al.
- "Hacking: The Art of Exploitation" - Jon Erickson
- "The IDA Pro Book" - Chris Eagle

### Sites Web
- [pwn.college](https://pwn.college) - Cours universitaire
- [ROP Emporium](https://ropemporium.com) - Challenges ROP
- [picoCTF](https://picoctf.org) - CTF pour débutants
- [OverTheWire](https://overthewire.org) - Wargames

### CTF Platforms
- [HackTheBox](https://hackthebox.eu)
- [TryHackMe](https://tryhackme.com)
- [VulnHub](https://vulnhub.com)

## 🏆 Objectifs de Formation

À la fin de cette formation, tu seras capable de :
- ✅ Analyser des binaires avec les outils appropriés
- ✅ Identifier et exploiter des buffer overflows
- ✅ Contourner les protections modernes (NX, ASLR, Canaries)
- ✅ Développer des exploits avec pwntools
- ✅ Participer efficacement à des CTF
- ✅ Comprendre l'exploitation heap et kernel (niveau avancé)

## 🚀 Prochaines Étapes

1. **Maintenant**: Configure ton environnement avec le script
2. **Aujourd'hui**: Résous le challenge level1
3. **Cette semaine**: Maîtrise GDB et l'analyse binaire
4. **Ce mois**: Complète tous les challenges de base

Bonne chance dans ton apprentissage du pwning ! 🎯
