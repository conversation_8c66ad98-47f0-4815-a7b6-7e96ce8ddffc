# 🔍 Guide d'Analyse Statique et Dynamique

## 📋 Vue d'ensemble

L'analyse de binaires est cruciale pour le pwning. Elle se divise en deux approches :
- **Analyse statique** : Examiner le binaire sans l'exécuter
- **Analyse dynamique** : Observer le comportement pendant l'exécution

## 🔧 Outils d'Analyse Statique

### 1. Informations de base
```bash
# Type de fichier
file binary
# Exemple: ELF 64-bit LSB executable, x86-64

# Protections activées
checksec binary
# Montre: NX, PIE, Canary, RELRO, etc.

# Strings dans le binaire
strings binary
# Révèle: messages, fonctions, chemins

# Taille et sections
size binary
ls -la binary
```

### 2. Analyse des headers ELF
```bash
# Headers complets
readelf -a binary

# Headers de programme
readelf -l binary

# Table des symboles
readelf -s binary

# Sections
readelf -S binary

# Informations de relocation
readelf -r binary
```

### 3. Désassemblage
```bash
# Désassemblage complet
objdump -d binary

# Avec syntaxe Intel
objdump -d -M intel binary

# Fonctions spécifiques
objdump -d binary | grep -A 20 "<main>"

# Avec adresses et opcodes
objdump -d -S binary
```

### 4. Analyse avec pwntools (Python)
```python
from pwn import *

# Charger le binaire
elf = ELF('./binary')

# Informations générales
print(f"Architecture: {elf.arch}")
print(f"Bits: {elf.bits}")
print(f"Endianness: {elf.endian}")

# Protections
print(f"NX: {elf.nx}")
print(f"PIE: {elf.pie}")
print(f"Canary: {elf.canary}")

# Symboles et adresses
print(f"Entry point: {hex(elf.entry)}")
if 'main' in elf.symbols:
    print(f"main(): {hex(elf.symbols['main'])}")

# Sections
for section in elf.sections:
    print(f"{section.name}: {hex(section.header.sh_addr)}")
```

## 🎮 Outils d'Analyse Dynamique

### 1. GDB (GNU Debugger)
```bash
# Lancement
gdb ./binary

# Points d'arrêt
break main
break *0x401000
break vulnerable_function

# Exécution
run [arguments]
continue
step (si)    # Step into
next (ni)    # Step over

# Examen des registres
info registers
print $rax
print $rsp
print $rip

# Examen de la mémoire
x/10gx $rsp        # 10 mots de 8 bytes en hexa
x/10i $rip         # 10 instructions à partir de RIP
x/s 0x401000       # String à l'adresse
x/20c $rsp         # 20 caractères

# Stack et frames
bt                 # Backtrace
frame 0            # Frame courante
info frame         # Info sur la frame

# Désassemblage
disas main
disas vulnerable_function
```

### 2. GDB avec GEF (GDB Enhanced Features)
```bash
# Installation
wget -O ~/.gdbinit-gef.py -q https://gef.blah.cat/py
echo "source ~/.gdbinit-gef.py" >> ~/.gdbinit

# Commandes GEF utiles
gef> checksec           # Protections
gef> vmmap              # Mapping mémoire
gef> stack 20           # Stack avec contexte
gef> registers          # Registres colorés
gef> hexdump $rsp       # Dump hexadécimal
gef> pattern create 200 # Créer pattern cyclique
gef> pattern offset $rsp # Trouver offset
```

### 3. Autres outils dynamiques
```bash
# System calls
strace ./binary

# Library calls
ltrace ./binary

# Valgrind (détection d'erreurs mémoire)
valgrind --tool=memcheck ./binary

# Analyse de performance
perf record ./binary
perf report
```

## 🎯 Méthodologie d'Analyse

### 1. Reconnaissance initiale
```bash
# Étape 1: Informations de base
file binary
checksec binary
strings binary | grep -i "flag\|password\|key"

# Étape 2: Structure du binaire
readelf -h binary        # Header ELF
readelf -S binary        # Sections
readelf -s binary        # Symboles
```

### 2. Analyse statique approfondie
```bash
# Désassemblage des fonctions importantes
objdump -d binary | grep -A 50 "<main>"
objdump -d binary | grep -A 50 "<vulnerable>"

# Recherche de gadgets ROP (si nécessaire)
ROPgadget --binary binary

# Analyse avec Ghidra/IDA (si disponible)
# - Décompilation
# - Analyse de flux
# - Identification des vulnérabilités
```

### 3. Analyse dynamique
```bash
# GDB avec points d'arrêt stratégiques
gdb ./binary
break main
break vulnerable_function
run

# Observation du comportement
# - État des registres
# - Contenu de la stack
# - Flux d'exécution
```

## 🔍 Identification des Vulnérabilités

### Buffer Overflow
```c
// Patterns à chercher
gets(buffer)           // Dangereux !
strcpy(dest, src)      // Sans vérification
sprintf(buffer, fmt)   // Format string + overflow
scanf("%s", buffer)    // Pas de limite
```

### Format String
```c
// Patterns dangereux
printf(user_input)     // Format string bug !
fprintf(file, user_input)
sprintf(buffer, user_input)
```

### Use-After-Free
```c
// Pattern typique
free(ptr);
// ... code ...
use(ptr);              // Utilisation après free !
```

## 🛠️ Exemple Pratique

### Analyse d'un binaire simple
```bash
# 1. Informations de base
$ file challenge1
challenge1: ELF 64-bit LSB executable, x86-64

$ checksec challenge1
[*] '/path/challenge1'
    Arch:     amd64-64-little
    RELRO:    Partial RELRO
    Stack:    No canary found
    NX:       NX disabled
    PIE:      No PIE (0x400000)

# 2. Strings intéressantes
$ strings challenge1
Enter your input:
You entered: %s
win
/bin/sh

# 3. Analyse des fonctions
$ objdump -d challenge1 | grep -A 20 "<main>"
0000000000401136 <main>:
  401136: push   %rbp
  401137: mov    %rsp,%rbp
  40113a: sub    $0x50,%rsp    # 80 bytes d'espace local
  ...

# 4. Test dynamique
$ gdb ./challenge1
(gdb) break main
(gdb) run
(gdb) disas main
(gdb) x/20gx $rsp
```

## 📊 Checklist d'Analyse

### Avant l'exploitation
- [ ] Type et architecture du binaire
- [ ] Protections activées (NX, PIE, Canary, RELRO)
- [ ] Fonctions importantes identifiées
- [ ] Vulnérabilités potentielles repérées
- [ ] Strings et constantes analysées

### Pendant l'analyse dynamique
- [ ] Points d'arrêt placés aux endroits critiques
- [ ] État de la stack observé
- [ ] Registres surveillés
- [ ] Flux d'exécution compris

### Préparation de l'exploit
- [ ] Offset calculé (pattern cyclique)
- [ ] Adresses cibles identifiées
- [ ] Contraintes d'exploitation comprises
- [ ] Payload construit et testé

## 🚀 Prochaines Étapes

1. **Pratique** : Utilise le challenge Python pour comprendre les concepts
2. **Installation** : Configure GDB avec GEF
3. **Exercices** : Analyse des binaires d'exemple
4. **CTF** : Applique sur de vrais challenges

## 💡 Conseils Pro

- **Toujours commencer par l'analyse statique** avant le dynamique
- **Prendre des notes** pendant l'analyse
- **Automatiser** les tâches répétitives avec des scripts
- **Comprendre** avant d'exploiter
- **Tester** les hypothèses avec GDB

L'analyse de binaires est un art qui s'améliore avec la pratique ! 🎯
