# 🔍 Fondamentaux OSINT - Guide Complet

## 📖 Qu'est-ce que l'OSINT ?

**OSINT** (Open Source Intelligence) = **Renseignement de sources ouvertes**

### Définition
L'OSINT consiste à **collecter, analyser et exploiter** des informations disponibles publiquement pour répondre à des questions spécifiques ou résoudre des problèmes.

### Sources Ouvertes
- **Internet** : Sites web, réseaux sociaux, forums
- **Médias** : Journaux, télévision, radio
- **Publications** : Rapports, études, brevets
- **Données publiques** : Registres, bases de données gouvernementales

## 🎯 Méthodologie OSINT Professionnelle

### 1. Phase de Planification
```
🎯 OBJECTIF
├── Que cherche-t-on exactement ?
├── Quel est le contexte ?
├── Quelles sont les contraintes ?
└── Quel est le délai ?

📋 SCOPE
├── Périmètre de recherche
├── Sources autorisées
├── Limites éthiques/légales
└── Critères de succès
```

### 2. Phase de Collecte
```
🔍 SOURCES PRIMAIRES
├── Sites web officiels
├── Réseaux sociaux
├── Bases de données publiques
└── Archives web

🔗 SOURCES SECONDAIRES
├── Articles de presse
├── Rapports d'analyse
├── Forums spécialisés
└── Blogs d'experts
```

### 3. Phase d'Analyse
```
🧠 TRAITEMENT
├── Vérification des sources
├── Corrélation des données
├── Identification des patterns
└── Évaluation de la fiabilité

📊 SYNTHÈSE
├── Timeline des événements
├── Cartographie des liens
├── Hypothèses et conclusions
└── Lacunes identifiées
```

### 4. Phase de Présentation
```
📝 RAPPORT
├── Résumé exécutif
├── Méthodologie utilisée
├── Résultats détaillés
└── Recommandations

🎯 LIVRABLES
├── Rapport principal
├── Annexes (preuves)
├── Timeline visuelle
└── Graphiques de liens
```

## 🌐 Types d'OSINT

### 1. HUMINT (Human Intelligence)
- **Personnes** : Profils, contacts, activités
- **Organisations** : Structure, employés, partenaires
- **Réseaux** : Relations, influences, communications

### 2. TECHINT (Technical Intelligence)
- **Domaines** : Whois, DNS, certificats
- **Infrastructure** : Serveurs, services, ports
- **Applications** : Technologies, versions, vulnérabilités

### 3. GEOINT (Geospatial Intelligence)
- **Localisation** : Adresses, coordonnées GPS
- **Imagerie** : Photos, satellites, streetview
- **Cartographie** : Cartes, plans, itinéraires

### 4. SIGINT (Signals Intelligence)
- **Communications** : Emails, messages, appels
- **Métadonnées** : Headers, timestamps, géolocalisation
- **Traces numériques** : Logs, historiques, caches

## 🛠️ Outils OSINT par Catégorie

### Moteurs de Recherche
```bash
# Google avec opérateurs avancés
site:example.com filetype:pdf
inurl:admin intitle:"login"
"<EMAIL>" -site:domain.com

# Moteurs spécialisés
Shodan.io          # Dispositifs connectés
Censys.io          # Certificats, services
Have I Been Pwned  # Fuites de données
```

### Réseaux Sociaux
```bash
# Recherche multi-plateformes
Sherlock          # Usernames sur 400+ sites
Social-Searcher   # Monitoring réseaux sociaux
Pipl.com          # Recherche de personnes
TinEye            # Recherche d'images inversée
```

### Domaines et Infrastructure
```bash
# Reconnaissance de domaines
whois domain.com
dig domain.com ANY
nslookup domain.com

# Outils spécialisés
Sublist3r         # Énumération sous-domaines
theHarvester      # Emails, sous-domaines
Maltego           # Visualisation de liens
```

### Géolocalisation
```bash
# Analyse d'images
exiftool image.jpg    # Métadonnées
Google Earth          # Imagerie satellite
Wikimapia            # Cartes collaboratives
```

## 🎯 Techniques OSINT Avancées

### Google Dorking Avancé
```bash
# Recherche de fichiers sensibles
site:target.com filetype:pdf "confidential"
site:target.com inurl:admin
site:target.com intitle:"index of"

# Recherche d'informations personnelles
"John Doe" site:linkedin.com
"<EMAIL>" -site:company.com
"************" OR "************"

# Recherche technique
site:target.com inurl:wp-admin
site:target.com "powered by"
inurl:".git" site:target.com
```

### Pivoting et Corrélation
```bash
# À partir d'un email
Email → Domaine → Whois → Autres domaines
Email → Réseaux sociaux → Contacts → Informations

# À partir d'une image
Image → Métadonnées → Géolocalisation → Contexte
Image → Recherche inversée → Sources → Informations

# À partir d'un username
Username → Sherlock → Profils → Informations
Username → Variations → Autres comptes → Corrélations
```

## 🛡️ Éthique et Légalité OSINT

### Règles Fondamentales
```
✅ AUTORISÉ
├── Sources publiquement accessibles
├── Informations déjà publiées
├── Recherche passive uniquement
└── Respect des ToS des plateformes

❌ INTERDIT
├── Hacking ou intrusion
├── Social engineering actif
├── Usurpation d'identité
└── Harcèlement ou stalking
```

### Bonnes Pratiques
- **Documenter** toutes les sources
- **Horodater** les captures d'écran
- **Respecter** la vie privée
- **Vérifier** les informations
- **Protéger** les données sensibles

## 🎮 OSINT pour les CTF

### Types de Challenges
```
🔍 RECHERCHE DE PERSONNES
├── Identification via photo
├── Recherche de profils sociaux
├── Corrélation d'informations
└── Timeline d'activités

🌍 GÉOLOCALISATION
├── Identification de lieux
├── Analyse de métadonnées
├── Recherche d'images inversée
└── Corrélation géographique

💻 RECONNAISSANCE TECHNIQUE
├── Énumération de domaines
├── Analyse d'infrastructure
├── Recherche de vulnérabilités
└── Corrélation technique
```

### Stratégies de Résolution
```
⚡ SPEED OSINT
├── Outils automatisés
├── Scripts personnalisés
├── Workflows optimisés
└── Recherches parallèles

🎯 MÉTHODOLOGIE
├── Analyse du challenge
├── Identification des indices
├── Recherche systématique
└── Vérification des résultats
```

## 📊 Framework d'Évaluation

### Fiabilité des Sources
```
A - Complètement fiable
B - Généralement fiable  
C - Assez fiable
D - Généralement peu fiable
E - Peu fiable
F - Fiabilité inconnue
```

### Crédibilité des Informations
```
1 - Confirmé par d'autres sources
2 - Probablement vrai
3 - Possiblement vrai
4 - Douteux
5 - Improbable
6 - Véracité inconnue
```

## 🚀 Exercices Pratiques

### Exercice 1: Recherche de Base
```
🎯 OBJECTIF: Trouver des informations sur "John Smith, CEO TechCorp"

📋 ÉTAPES:
1. Google: "John Smith" CEO TechCorp
2. LinkedIn: Profil professionnel
3. Site entreprise: Page équipe
4. Réseaux sociaux: Profils personnels
5. Articles presse: Mentions

📝 LIVRABLE: Fiche de synthèse
```

### Exercice 2: Géolocalisation
```
🎯 OBJECTIF: Localiser une photo de restaurant

📋 ÉTAPES:
1. Analyse métadonnées (exiftool)
2. Recherche inversée (TinEye, Google)
3. Indices visuels (enseignes, architecture)
4. Corrélation géographique
5. Vérification finale

📝 LIVRABLE: Coordonnées GPS + justification
```

## 🎯 Prochaines Étapes

1. **Maîtriser** les concepts fondamentaux
2. **Pratiquer** avec les exercices
3. **Installer** les outils essentiels
4. **Résoudre** les premiers challenges
5. **Développer** sa méthodologie personnelle

## 💡 Points Clés à Retenir

- **L'OSINT est légal** mais doit respecter l'éthique
- **La méthodologie** est plus importante que les outils
- **La vérification** des sources est cruciale
- **La documentation** est essentielle
- **La pratique** développe l'intuition

**L'OSINT est un art qui se perfectionne avec l'expérience !** 🕵️‍♂️

---

*Guide créé pour une formation OSINT complète et éthique*
