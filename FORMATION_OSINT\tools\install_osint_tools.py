#!/usr/bin/env python3
"""
Installation et Configuration des Outils OSINT Essentiels
Formation OSINT Master pour CTF

Ce script installe et configure tous les outils OSINT nécessaires
pour devenir un expert en reconnaissance et investigation.
"""

import subprocess
import sys
import os
import requests
import json
from pathlib import Path

class OSINTToolsInstaller:
    """Installateur d'outils OSINT"""
    
    def __init__(self):
        self.tools_installed = []
        self.tools_failed = []
        
    def run_command(self, command, description=""):
        """Exécute une commande et gère les erreurs"""
        try:
            print(f"🔧 {description}...")
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {description} - Succès")
                return True
            else:
                print(f"❌ {description} - Échec: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ {description} - Erreur: {e}")
            return False
    
    def install_python_tools(self):
        """Installe les outils Python OSINT"""
        print("🐍 INSTALLATION DES OUTILS PYTHON OSINT")
        print("=" * 50)
        
        python_tools = {
            'requests': 'Requêtes HTTP',
            'beautifulsoup4': 'Parsing HTML',
            'lxml': 'Parser XML/HTML rapide',
            'selenium': 'Automation navigateur',
            'Pillow': 'Traitement d\'images',
            'exifread': 'Lecture métadonnées EXIF',
            'python-whois': 'Requêtes WHOIS',
            'dnspython': 'Requêtes DNS',
            'shodan': 'API Shodan',
            'tweepy': 'API Twitter',
            'instaloader': 'Téléchargeur Instagram',
            'youtube-dl': 'Téléchargeur vidéos',
            'phonenumbers': 'Validation numéros téléphone',
            'geopy': 'Géocodage et géolocalisation',
            'folium': 'Cartes interactives',
            'networkx': 'Analyse de graphes',
            'matplotlib': 'Graphiques et visualisation',
            'pandas': 'Analyse de données',
            'numpy': 'Calculs numériques'
        }
        
        for tool, description in python_tools.items():
            if self.run_command(f"pip install {tool}", f"Installation {tool} ({description})"):
                self.tools_installed.append(tool)
            else:
                self.tools_failed.append(tool)
        
        print(f"\n📊 Outils Python: {len(self.tools_installed)} installés, {len(self.tools_failed)} échecs")
    
    def install_git_tools(self):
        """Installe les outils depuis GitHub"""
        print("\n🔗 INSTALLATION DES OUTILS GITHUB")
        print("=" * 50)
        
        git_tools = {
            'theHarvester': {
                'url': 'https://github.com/laramies/theHarvester.git',
                'description': 'Collecte emails, sous-domaines, IPs'
            },
            'Sublist3r': {
                'url': 'https://github.com/aboul3la/Sublist3r.git',
                'description': 'Énumération sous-domaines'
            },
            'sherlock': {
                'url': 'https://github.com/sherlock-project/sherlock.git',
                'description': 'Recherche usernames sur 400+ sites'
            },
            'social-analyzer': {
                'url': 'https://github.com/qeeqbox/social-analyzer.git',
                'description': 'Analyse profils réseaux sociaux'
            },
            'photon': {
                'url': 'https://github.com/s0md3v/Photon.git',
                'description': 'Web crawler OSINT'
            },
            'spiderfoot': {
                'url': 'https://github.com/smicallef/spiderfoot.git',
                'description': 'Plateforme OSINT automatisée'
            }
        }
        
        tools_dir = Path("../tools/github_tools")
        tools_dir.mkdir(exist_ok=True)
        
        for tool_name, tool_info in git_tools.items():
            tool_path = tools_dir / tool_name
            if not tool_path.exists():
                if self.run_command(
                    f"git clone {tool_info['url']} {tool_path}",
                    f"Clonage {tool_name} ({tool_info['description']})"
                ):
                    # Installation des dépendances si requirements.txt existe
                    req_file = tool_path / "requirements.txt"
                    if req_file.exists():
                        self.run_command(
                            f"pip install -r {req_file}",
                            f"Installation dépendances {tool_name}"
                        )
                    self.tools_installed.append(tool_name)
                else:
                    self.tools_failed.append(tool_name)
            else:
                print(f"✅ {tool_name} déjà installé")
                self.tools_installed.append(tool_name)
    
    def create_osint_scripts(self):
        """Crée des scripts OSINT personnalisés"""
        print("\n📝 CRÉATION DES SCRIPTS OSINT PERSONNALISÉS")
        print("=" * 50)
        
        scripts_dir = Path("../tools/custom_scripts")
        scripts_dir.mkdir(exist_ok=True)
        
        # Script de recherche Google automatisée
        google_script = '''#!/usr/bin/env python3
"""
Script de Google Dorking Automatisé
"""
import requests
from bs4 import BeautifulSoup
import time
import random

def google_dork(query, num_results=10):
    """Effectue une recherche Google avec dorking"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    results = []
    for start in range(0, num_results, 10):
        url = f"https://www.google.com/search?q={query}&start={start}"
        
        try:
            response = requests.get(url, headers=headers)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            for result in soup.find_all('div', class_='g'):
                title_elem = result.find('h3')
                link_elem = result.find('a')
                
                if title_elem and link_elem:
                    title = title_elem.get_text()
                    link = link_elem.get('href')
                    results.append({'title': title, 'url': link})
            
            time.sleep(random.uniform(1, 3))  # Éviter le rate limiting
            
        except Exception as e:
            print(f"Erreur: {e}")
            break
    
    return results

if __name__ == "__main__":
    query = input("Entrez votre requête Google: ")
    results = google_dork(query)
    
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['title']}")
        print(f"   {result['url']}")
        print()
'''
        
        with open(scripts_dir / "google_dork.py", "w", encoding="utf-8") as f:
            f.write(google_script)
        
        print("✅ Script Google Dorking créé")
        
        # Script d'analyse de métadonnées
        metadata_script = '''#!/usr/bin/env python3
"""
Analyseur de Métadonnées d'Images
"""
from PIL import Image
from PIL.ExifTags import TAGS
import os
import sys

def extract_metadata(image_path):
    """Extrait les métadonnées d'une image"""
    try:
        image = Image.open(image_path)
        exifdata = image.getexif()
        
        metadata = {}
        for tag_id in exifdata:
            tag = TAGS.get(tag_id, tag_id)
            data = exifdata.get(tag_id)
            metadata[tag] = data
        
        return metadata
    except Exception as e:
        print(f"Erreur lors de l'extraction: {e}")
        return {}

def display_metadata(metadata):
    """Affiche les métadonnées de façon lisible"""
    if not metadata:
        print("Aucune métadonnée trouvée")
        return
    
    print("📊 MÉTADONNÉES EXTRAITES:")
    print("=" * 40)
    
    for tag, value in metadata.items():
        print(f"{tag:20}: {value}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python metadata_analyzer.py <image_path>")
        sys.exit(1)
    
    image_path = sys.argv[1]
    if not os.path.exists(image_path):
        print(f"Fichier non trouvé: {image_path}")
        sys.exit(1)
    
    metadata = extract_metadata(image_path)
    display_metadata(metadata)
'''
        
        with open(scripts_dir / "metadata_analyzer.py", "w", encoding="utf-8") as f:
            f.write(metadata_script)
        
        print("✅ Script d'analyse de métadonnées créé")
    
    def create_config_files(self):
        """Crée les fichiers de configuration"""
        print("\n⚙️ CRÉATION DES FICHIERS DE CONFIGURATION")
        print("=" * 50)
        
        config_dir = Path("../tools/configs")
        config_dir.mkdir(exist_ok=True)
        
        # Configuration API keys
        api_config = {
            "shodan": {
                "api_key": "YOUR_SHODAN_API_KEY",
                "description": "Obtenez votre clé sur https://account.shodan.io/"
            },
            "twitter": {
                "api_key": "YOUR_TWITTER_API_KEY",
                "api_secret": "YOUR_TWITTER_API_SECRET",
                "access_token": "YOUR_ACCESS_TOKEN",
                "access_token_secret": "YOUR_ACCESS_TOKEN_SECRET",
                "description": "Créez une app sur https://developer.twitter.com/"
            },
            "google": {
                "api_key": "YOUR_GOOGLE_API_KEY",
                "search_engine_id": "YOUR_SEARCH_ENGINE_ID",
                "description": "Console développeur Google"
            }
        }
        
        with open(config_dir / "api_keys.json", "w", encoding="utf-8") as f:
            json.dump(api_config, f, indent=2)
        
        print("✅ Fichier de configuration API créé")
        
        # Liste de Google Dorks
        google_dorks = [
            'site:target.com filetype:pdf',
            'site:target.com inurl:admin',
            'site:target.com intitle:"index of"',
            'site:target.com "password" filetype:txt',
            'site:target.com "confidential" OR "secret"',
            'inurl:wp-admin site:target.com',
            'site:target.com "powered by"',
            'site:target.com inurl:login',
            'site:target.com filetype:sql',
            'site:target.com "database" filetype:sql'
        ]
        
        with open(config_dir / "google_dorks.txt", "w", encoding="utf-8") as f:
            for dork in google_dorks:
                f.write(f"{dork}\n")
        
        print("✅ Liste de Google Dorks créée")
    
    def create_usage_guide(self):
        """Crée un guide d'utilisation des outils"""
        print("\n📚 CRÉATION DU GUIDE D'UTILISATION")
        print("=" * 50)
        
        guide_content = '''# 🛠️ Guide d'Utilisation des Outils OSINT

## 🔧 Outils Installés

### Python Tools
- **requests, beautifulsoup4**: Web scraping
- **selenium**: Automation navigateur
- **shodan**: Recherche dispositifs connectés
- **python-whois**: Informations domaines
- **exifread**: Métadonnées images

### GitHub Tools
- **theHarvester**: `python theHarvester.py -d domain.com -b google`
- **Sublist3r**: `python sublist3r.py -d domain.com`
- **sherlock**: `python sherlock.py username`
- **photon**: `python photon.py -u https://target.com`

### Scripts Personnalisés
- **google_dork.py**: Recherche Google automatisée
- **metadata_analyzer.py**: Analyse métadonnées images

## 🚀 Exemples d'Utilisation

### Reconnaissance de Domaine
```bash
# Informations WHOIS
python -c "import whois; print(whois.whois('domain.com'))"

# Sous-domaines
python Sublist3r/sublist3r.py -d domain.com

# Emails et informations
python theHarvester/theHarvester.py -d domain.com -b all
```

### Recherche de Personnes
```bash
# Usernames sur réseaux sociaux
python sherlock/sherlock.py john_doe

# Recherche Google ciblée
python custom_scripts/google_dork.py
```

### Analyse d'Images
```bash
# Métadonnées
python custom_scripts/metadata_analyzer.py image.jpg

# Recherche inversée (manuel)
# Utiliser TinEye.com ou Google Images
```

## ⚙️ Configuration

1. **API Keys**: Éditez `configs/api_keys.json`
2. **Google Dorks**: Personnalisez `configs/google_dorks.txt`
3. **Proxies**: Configurez si nécessaire pour éviter le rate limiting

## 🎯 Workflows Recommandés

### Investigation Personne
1. Google: "Nom Prénom"
2. Sherlock: username
3. Réseaux sociaux manuels
4. Corrélation informations

### Reconnaissance Technique
1. WHOIS domaine
2. Sublist3r sous-domaines
3. theHarvester emails
4. Shodan infrastructure

Bonne investigation ! 🕵️‍♂️
'''
        
        with open(Path("../tools/USAGE_GUIDE.md"), "w", encoding="utf-8") as f:
            f.write(guide_content)
        
        print("✅ Guide d'utilisation créé")
    
    def run_installation(self):
        """Lance l'installation complète"""
        print("🚀 INSTALLATION DES OUTILS OSINT MASTER")
        print("=" * 60)
        print("Installation complète pour devenir un expert OSINT CTF")
        print("=" * 60)
        
        # Installation des outils
        self.install_python_tools()
        self.install_git_tools()
        self.create_osint_scripts()
        self.create_config_files()
        self.create_usage_guide()
        
        # Résumé final
        print("\n🎉 INSTALLATION TERMINÉE !")
        print("=" * 40)
        print(f"✅ Outils installés: {len(self.tools_installed)}")
        print(f"❌ Échecs: {len(self.tools_failed)}")
        
        if self.tools_failed:
            print(f"\n⚠️ Outils échoués: {', '.join(self.tools_failed)}")
            print("💡 Réessayez manuellement si nécessaire")
        
        print("\n🎯 PROCHAINES ÉTAPES:")
        print("1. Configurez vos API keys dans configs/api_keys.json")
        print("2. Lisez le guide d'utilisation: tools/USAGE_GUIDE.md")
        print("3. Testez les outils avec les challenges practice/")
        print("4. Commencez par les fondamentaux dans guides/")
        
        print("\n🏆 Vous êtes maintenant équipé pour l'OSINT master !")

def main():
    """Fonction principale"""
    installer = OSINTToolsInstaller()
    installer.run_installation()

if __name__ == "__main__":
    main()
'''

NOTES D'INSTALLATION:

1. PRÉREQUIS:
   - Python 3.7+
   - Git installé
   - Connexion Internet

2. OUTILS INSTALLÉS:
   - Outils Python pour web scraping et analyse
   - Outils GitHub pour OSINT spécialisé
   - Scripts personnalisés optimisés
   - Configurations et guides

3. POST-INSTALLATION:
   - Configurer les API keys
   - Tester les outils
   - Lire les guides
   - Commencer les challenges

4. SUPPORT:
   - Tous les outils sont open source
   - Documentation incluse
   - Exemples d'utilisation fournis
'''
