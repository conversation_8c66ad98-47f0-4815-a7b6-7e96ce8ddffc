#!/usr/bin/env python3
"""
Exercice pratique : Analyse de binaires et assembleur
Formation PWN pour CTF

Ce script simule l'analyse d'un programme vulnérable et enseigne
les concepts fondamentaux de l'assembleur x64.
"""

from pwn import *
import struct

def demonstrate_registers():
    """Démonstration des registres x64"""
    print("🔧 REGISTRES x64 - Concepts Fondamentaux")
    print("=" * 50)
    
    print("📋 Registres principaux (64-bit):")
    registers = {
        'RAX': 'Accumulateur (valeur de retour)',
        'RBX': 'Base (usage général)', 
        'RCX': 'Compteur (4ème argument)',
        'RDX': 'Données (3ème argument)',
        'RSI': 'Source Index (2ème argument)',
        'RDI': 'Destination Index (1er argument)',
        'RSP': 'Stack Pointer (pointeur de pile)',
        'RBP': 'Base Pointer (base de frame)',
        'RIP': 'Instruction Pointer (prochaine instruction)'
    }
    
    for reg, desc in registers.items():
        print(f"  {reg:3} - {desc}")
    
    print("\n💡 Calling Convention Linux x64:")
    print("  Arguments: RDI, RSI, RDX, RCX, R8, R9, puis stack")
    print("  Retour: RAX")
    print()

def demonstrate_stack():
    """Démonstration de la structure de la stack"""
    print("🏗️ STRUCTURE DE LA STACK")
    print("=" * 50)
    
    # Simulation d'une stack
    stack_example = [
        ("0x7fff12345678", "Arguments de main()"),
        ("0x7fff12345670", "Adresse de retour (RIP sauvegardé)"),
        ("0x7fff12345668", "RBP sauvegardé"),
        ("0x7fff12345660", "Variable locale 1"),
        ("0x7fff12345658", "Variable locale 2 (buffer[64])"),
        ("0x7fff12345650", "..."),
        ("0x7fff12345620", "Fin du buffer <- RSP pointe ici")
    ]
    
    print("Adresses hautes")
    for addr, desc in stack_example:
        print(f"  {addr} | {desc}")
    print("Adresses basses")
    
    print("\n💡 Points importants:")
    print("  - La stack grandit vers le bas (adresses décroissantes)")
    print("  - RSP pointe vers le sommet de la stack")
    print("  - RBP sert de référence pour les variables locales")
    print("  - L'adresse de retour est cruciale pour l'exploitation")
    print()

def demonstrate_buffer_overflow():
    """Démonstration conceptuelle d'un buffer overflow"""
    print("💥 BUFFER OVERFLOW - Concept")
    print("=" * 50)
    
    print("Code vulnérable (conceptuel):")
    print("""
    void vulnerable(char *input) {
        char buffer[64];        // Buffer de 64 bytes
        strcpy(buffer, input);  // PAS de vérification de taille !
        printf("Input: %s\\n", buffer);
    }
    """)
    
    print("🎯 Exploitation:")
    print("  1. Buffer normal (64 bytes): 'A' * 64")
    print("     -> Pas de problème")
    
    print("  2. Buffer overflow (72+ bytes): 'A' * 72")
    print("     -> Écrase RBP sauvegardé")
    
    print("  3. Contrôle RIP (80+ bytes): 'A' * 72 + adresse")
    print("     -> Contrôle l'exécution du programme !")
    
    # Simulation visuelle
    print("\n📊 Visualisation de l'overflow:")
    normal_input = "A" * 64
    overflow_input = "A" * 72 + "BBBBBBBB"
    
    print(f"  Input normal:  [{normal_input[:20]}...] (64 bytes)")
    print(f"  Input overflow: [{overflow_input[:20]}...BBBBBBBB] (80 bytes)")
    print("                   ^buffer^      ^RBP^ ^RIP^")
    print()

def create_payload_example():
    """Exemple de création de payload"""
    print("🔧 CRÉATION DE PAYLOAD")
    print("=" * 50)
    
    # Adresse fictive de la fonction win()
    win_addr = 0x401234
    
    print("Objectif: Faire exécuter la fonction win()")
    print(f"Adresse de win(): {hex(win_addr)}")
    
    print("\n🛠️ Construction du payload:")
    print("  1. Padding pour atteindre l'adresse de retour")
    print("  2. Adresse de la fonction win()")
    
    # Construction du payload avec pwntools
    offset = 72  # 64 bytes buffer + 8 bytes RBP
    payload = b'A' * offset + p64(win_addr)
    
    print(f"\nPayload (Python/pwntools):")
    print(f"  offset = {offset}")
    print(f"  payload = b'A' * {offset} + p64({hex(win_addr)})")
    print(f"  Taille totale: {len(payload)} bytes")
    
    # Visualisation hexadécimale
    print(f"\nPayload en hexadécimal:")
    print(f"  {payload.hex()}")
    
    # Décomposition
    print(f"\nDécomposition:")
    print(f"  Padding: {payload[:offset].hex()}")
    print(f"  Adresse: {payload[offset:].hex()} (little-endian)")
    print()

def analyze_protection_bypass():
    """Analyse des protections et techniques de bypass"""
    print("🛡️ PROTECTIONS ET BYPASS")
    print("=" * 50)
    
    protections = {
        'NX/DEP': {
            'description': 'Pile non-exécutable',
            'bypass': 'ROP (Return Oriented Programming)',
            'technique': 'Chaîner des gadgets existants'
        },
        'ASLR': {
            'description': 'Randomisation des adresses',
            'bypass': 'Information leaks',
            'technique': 'Fuiter des adresses pour calculer la base'
        },
        'Stack Canary': {
            'description': 'Valeur de contrôle sur la stack',
            'bypass': 'Leak du canary ou bypass',
            'technique': 'Fuiter le canary ou utiliser d\'autres vulns'
        },
        'PIE': {
            'description': 'Code à position indépendante',
            'bypass': 'Leak d\'adresse code',
            'technique': 'Fuiter une adresse du binaire'
        }
    }
    
    for prot, info in protections.items():
        print(f"🔒 {prot}:")
        print(f"   Description: {info['description']}")
        print(f"   Bypass: {info['bypass']}")
        print(f"   Technique: {info['technique']}")
        print()

def practical_exercise():
    """Exercice pratique avec pwntools"""
    print("🎮 EXERCICE PRATIQUE")
    print("=" * 50)
    
    print("Créons un pattern cyclique pour trouver l'offset:")
    
    # Génération d'un pattern cyclique
    pattern = cyclic(100)
    print(f"Pattern généré: {pattern[:50]}...")
    
    print(f"\nLongueur du pattern: {len(pattern)} bytes")
    
    # Simulation de crash à une adresse spécifique
    crash_value = b'laaa'  # Partie du pattern cyclique
    offset = cyclic_find(crash_value)
    
    print(f"Si le programme crash avec la valeur: {crash_value}")
    print(f"L'offset est: {offset} bytes")
    
    print("\n💡 Utilisation:")
    print("  1. Génère un pattern avec cyclic(200)")
    print("  2. Lance le programme avec ce pattern")
    print("  3. Note la valeur qui cause le crash")
    print("  4. Trouve l'offset avec cyclic_find(valeur)")
    print("  5. Construis ton payload: b'A' * offset + adresse")
    print()

def main():
    """Fonction principale - Formation assembleur x64"""
    print("🎯 FORMATION PWN - ASSEMBLEUR x64 PRATIQUE")
    print("=" * 60)
    print("Formation complète pour devenir un pwner efficace")
    print("=" * 60)
    print()
    
    # Exécution des démonstrations
    demonstrate_registers()
    demonstrate_stack()
    demonstrate_buffer_overflow()
    create_payload_example()
    analyze_protection_bypass()
    practical_exercise()
    
    print("🚀 PROCHAINES ÉTAPES:")
    print("=" * 50)
    print("1. Étudie le guide assembleur_x64_basics.md")
    print("2. Pratique avec le challenge level1")
    print("3. Installe un compilateur C (MinGW ou WSL)")
    print("4. Compile et analyse tes premiers binaires")
    print("5. Maîtrise GDB pour le debugging")
    print()
    print("🎯 Tu es maintenant prêt pour le pwning pratique !")

if __name__ == "__main__":
    main()
