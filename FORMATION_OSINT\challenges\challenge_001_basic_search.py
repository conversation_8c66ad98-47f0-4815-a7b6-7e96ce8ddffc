#!/usr/bin/env python3
"""
CHALLENGE OSINT 001: Recherche de Base
Niveau: Débutant
Objectif: Maîtriser les techniques de recherche fondamentales

Ce challenge enseigne les bases de la recherche OSINT avec des exercices progressifs.
"""

import random
import json
from datetime import datetime
import webbrowser
import urllib.parse

class OSINTChallenge001:
    """Challenge OSINT de base - Recherche fondamentale"""
    
    def __init__(self):
        self.score = 0
        self.max_score = 100
        self.challenges_completed = []
        
        # Données fictives pour l'entraînement
        self.sample_data = {
            'person': {
                'name': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '+33 1 23 45 67 89',
                'company': 'TechCorp Solutions',
                'linkedin': 'linkedin.com/in/jean-dupont-tech',
                'twitter': '@jeandupont_tech'
            },
            'company': {
                'name': 'TechCorp Solutions',
                'domain': 'techcorp-solutions.fr',
                'address': '123 Avenue des Champs, 75008 Paris',
                'phone': '+33 1 23 45 67 00',
                'employees': ['<PERSON>', '<PERSON>', '<PERSON>']
            },
            'domain': {
                'name': 'techcorp-solutions.fr',
                'ip': '*************',
                'registrar': 'OVH',
                'creation_date': '2020-03-15',
                'subdomains': ['www', 'mail', 'ftp', 'admin']
            }
        }
    
    def show_intro(self):
        """Affiche l'introduction du challenge"""
        print("🕵️ CHALLENGE OSINT 001: RECHERCHE DE BASE")
        print("=" * 55)
        print("🎯 Objectif: Maîtriser les techniques de recherche OSINT")
        print("📊 Niveau: Débutant")
        print("⏱️ Durée estimée: 30-45 minutes")
        print("🏆 Score maximum: 100 points")
        print()
        print("📚 Ce que vous allez apprendre:")
        print("  • Techniques de recherche Google avancées")
        print("  • Recherche d'informations sur les personnes")
        print("  • Reconnaissance de domaines et entreprises")
        print("  • Corrélation d'informations")
        print("  • Vérification de sources")
        print()
        print("⚠️ IMPORTANT: Utilisez uniquement des sources publiques !")
        print("=" * 55)
        print()
    
    def challenge_1_google_dorking(self):
        """Challenge 1: Google Dorking"""
        print("🔍 CHALLENGE 1: GOOGLE DORKING (25 points)")
        print("-" * 40)
        print("Vous devez trouver des informations sur 'TechCorp Solutions'")
        print("en utilisant des techniques de Google Dorking.")
        print()
        
        questions = [
            {
                'question': 'Quelle requête Google utiliseriez-vous pour trouver des fichiers PDF sur le site techcorp-solutions.fr ?',
                'options': [
                    'A) site:techcorp-solutions.fr PDF',
                    'B) site:techcorp-solutions.fr filetype:pdf',
                    'C) techcorp-solutions.fr "PDF"',
                    'D) filetype:pdf techcorp-solutions.fr'
                ],
                'correct': 'B',
                'explanation': 'L\'opérateur "filetype:" est spécifique pour rechercher des types de fichiers.'
            },
            {
                'question': 'Pour trouver des pages d\'administration sur le site, quelle requête utiliseriez-vous ?',
                'options': [
                    'A) site:techcorp-solutions.fr admin',
                    'B) site:techcorp-solutions.fr inurl:admin',
                    'C) techcorp-solutions.fr "admin"',
                    'D) admin site:techcorp-solutions.fr'
                ],
                'correct': 'B',
                'explanation': 'L\'opérateur "inurl:" recherche dans l\'URL de la page.'
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"\nQuestion {i}:")
            print(q['question'])
            for option in q['options']:
                print(f"  {option}")
            
            answer = input("\nVotre réponse (A/B/C/D): ").upper().strip()
            
            if answer == q['correct']:
                print("✅ Correct ! +12.5 points")
                print(f"💡 Explication: {q['explanation']}")
                score += 12.5
            else:
                print(f"❌ Incorrect. La bonne réponse était {q['correct']}")
                print(f"💡 Explication: {q['explanation']}")
        
        self.score += score
        print(f"\n📊 Score Challenge 1: {score}/25 points")
        return score == 25
    
    def challenge_2_person_research(self):
        """Challenge 2: Recherche sur une personne"""
        print("\n👤 CHALLENGE 2: RECHERCHE DE PERSONNE (25 points)")
        print("-" * 40)
        print("Vous devez rassembler des informations sur 'Jean Dupont'")
        print("en utilisant les indices fournis.")
        print()
        
        # Afficher les indices
        print("🔍 Indices disponibles:")
        print(f"  • Nom: {self.sample_data['person']['name']}")
        print(f"  • Email: {self.sample_data['person']['email']}")
        print(f"  • Entreprise: {self.sample_data['person']['company']}")
        print()
        
        questions = [
            {
                'question': 'Quel domaine d\'email utilise Jean Dupont ?',
                'answer': 'techcorp.fr',
                'points': 5
            },
            {
                'question': 'Dans quelle entreprise travaille-t-il ?',
                'answer': 'TechCorp Solutions',
                'points': 5
            },
            {
                'question': 'Quelle requête Google utiliseriez-vous pour trouver son profil LinkedIn ?',
                'answer': 'site:linkedin.com "Jean Dupont" TechCorp',
                'points': 10,
                'flexible': True
            },
            {
                'question': 'Comment pourriez-vous vérifier si son email a été compromis dans des fuites de données ?',
                'answer': 'Have I Been Pwned',
                'points': 5,
                'flexible': True
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"\nQuestion {i} ({q['points']} points):")
            print(q['question'])
            
            user_answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                # Réponse flexible - vérifier les mots-clés
                keywords = q['answer'].lower().split()
                user_lower = user_answer.lower()
                if any(keyword in user_lower for keyword in keywords):
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
            else:
                if user_answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
        
        self.score += score
        print(f"\n📊 Score Challenge 2: {score}/25 points")
        return score == 25
    
    def challenge_3_domain_recon(self):
        """Challenge 3: Reconnaissance de domaine"""
        print("\n🌐 CHALLENGE 3: RECONNAISSANCE DE DOMAINE (25 points)")
        print("-" * 40)
        print("Analysez le domaine 'techcorp-solutions.fr' et répondez aux questions.")
        print()
        
        # Simuler des informations WHOIS
        print("🔍 Informations WHOIS simulées:")
        domain_info = self.sample_data['domain']
        print(f"  • Domaine: {domain_info['name']}")
        print(f"  • IP: {domain_info['ip']}")
        print(f"  • Registrar: {domain_info['registrar']}")
        print(f"  • Date de création: {domain_info['creation_date']}")
        print()
        
        questions = [
            {
                'question': 'Quel est le registrar du domaine ?',
                'answer': domain_info['registrar'],
                'points': 5
            },
            {
                'question': 'Quelle commande utiliseriez-vous pour obtenir les informations WHOIS ?',
                'answer': 'whois techcorp-solutions.fr',
                'points': 5
            },
            {
                'question': 'Comment trouveriez-vous les sous-domaines de ce domaine ?',
                'answer': 'Sublist3r',
                'points': 10,
                'flexible': True
            },
            {
                'question': 'Quel outil utiliseriez-vous pour scanner les ports ouverts sur l\'IP ?',
                'answer': 'nmap',
                'points': 5,
                'flexible': True
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"\nQuestion {i} ({q['points']} points):")
            print(q['question'])
            
            user_answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                keywords = q['answer'].lower().split()
                user_lower = user_answer.lower()
                if any(keyword in user_lower for keyword in keywords):
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse possible: {q['answer']}")
            else:
                if user_answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
        
        self.score += score
        print(f"\n📊 Score Challenge 3: {score}/25 points")
        return score == 25
    
    def challenge_4_correlation(self):
        """Challenge 4: Corrélation d'informations"""
        print("\n🔗 CHALLENGE 4: CORRÉLATION D'INFORMATIONS (25 points)")
        print("-" * 40)
        print("Utilisez toutes les informations collectées pour répondre aux questions de synthèse.")
        print()
        
        questions = [
            {
                'question': 'Combien d\'employés avez-vous identifiés chez TechCorp Solutions ?',
                'answer': '3',
                'points': 5
            },
            {
                'question': 'Quel est le <NAME_EMAIL> et techcorp-solutions.fr ?',
                'answer': 'même entreprise',
                'points': 10,
                'flexible': True
            },
            {
                'question': 'Si vous vouliez contacter l\'entreprise, quelles informations avez-vous ?',
                'answer': 'téléphone, adresse, email',
                'points': 10,
                'flexible': True
            }
        ]
        
        score = 0
        for i, q in enumerate(questions, 1):
            print(f"\nQuestion {i} ({q['points']} points):")
            print(q['question'])
            
            user_answer = input("Votre réponse: ").strip()
            
            if q.get('flexible'):
                keywords = q['answer'].lower().split()
                user_lower = user_answer.lower()
                if any(keyword in user_lower for keyword in keywords):
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse possible: {q['answer']}")
            else:
                if user_answer.lower() == q['answer'].lower():
                    print(f"✅ Correct ! +{q['points']} points")
                    score += q['points']
                else:
                    print(f"❌ Réponse attendue: {q['answer']}")
        
        self.score += score
        print(f"\n📊 Score Challenge 4: {score}/25 points")
        return score == 25
    
    def show_final_results(self):
        """Affiche les résultats finaux"""
        print("\n🏆 RÉSULTATS FINAUX")
        print("=" * 40)
        print(f"Score total: {self.score}/{self.max_score} points")
        print(f"Pourcentage: {(self.score/self.max_score)*100:.1f}%")
        print()
        
        if self.score >= 90:
            print("🥇 EXCELLENT ! Vous maîtrisez les bases de l'OSINT !")
            print("🚀 Prêt pour les challenges intermédiaires.")
        elif self.score >= 70:
            print("🥈 BIEN ! Bonnes bases, quelques points à améliorer.")
            print("📚 Révisez les concepts où vous avez eu des difficultés.")
        elif self.score >= 50:
            print("🥉 PASSABLE. Il faut encore travailler les fondamentaux.")
            print("🔄 Recommencez le challenge après avoir étudié les guides.")
        else:
            print("📚 Il faut d'abord étudier les guides théoriques.")
            print("🔄 Reprenez le challenge après avoir lu les fondamentaux.")
        
        print("\n🎯 PROCHAINES ÉTAPES:")
        if self.score >= 70:
            print("  • Challenge 002: Géolocalisation")
            print("  • Challenge 003: Réseaux sociaux")
            print("  • Challenge 004: OSINT technique avancé")
        else:
            print("  • Relire guides/fondamentaux_osint.md")
            print("  • Pratiquer les outils de base")
            print("  • Refaire ce challenge")
        
        print("\n💡 CONSEILS:")
        print("  • L'OSINT demande de la pratique régulière")
        print("  • Toujours vérifier ses sources")
        print("  • Documenter sa méthodologie")
        print("  • Respecter l'éthique et la légalité")
    
    def run_challenge(self):
        """Lance le challenge complet"""
        self.show_intro()
        
        input("Appuyez sur Entrée pour commencer...")
        print()
        
        # Lancer les 4 challenges
        self.challenge_1_google_dorking()
        self.challenge_2_person_research()
        self.challenge_3_domain_recon()
        self.challenge_4_correlation()
        
        # Résultats finaux
        self.show_final_results()

def main():
    """Fonction principale"""
    challenge = OSINTChallenge001()
    challenge.run_challenge()

if __name__ == "__main__":
    main()

"""
OBJECTIFS PÉDAGOGIQUES:

1. RECHERCHE DE BASE:
   - Google Dorking
   - Opérateurs de recherche
   - Sources d'information

2. MÉTHODOLOGIE:
   - Approche systématique
   - Vérification des sources
   - Documentation des résultats

3. CORRÉLATION:
   - Liens entre informations
   - Validation croisée
   - Synthèse des données

4. OUTILS:
   - Introduction aux outils OSINT
   - Commandes de base
   - Workflows efficaces

Ce challenge prépare aux techniques OSINT plus avancées
et aux challenges CTF réels.
"""
