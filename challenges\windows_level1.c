/*
 * CHALLENGE 1: Buffer Overflow Basique (Version Windows)
 * Objectif: Comprendre et exploiter un buffer overflow simple
 * Difficulté: Débutant
 * 
 * Compilation: gcc -g -fno-stack-protector -z execstack -m64 windows_level1.c -o windows_level1.exe
 * Alternative: gcc -g -O0 -fno-stack-protector windows_level1.c -o windows_level1.exe
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// Fonction secrète que nous voulons exécuter
void win() {
    printf("\n🎉 FÉLICITATIONS ! Tu as réussi à exploiter le buffer overflow !\n");
    printf("🏆 FLAG: CTF{windows_buffer_overflow_mastered}\n");
    printf("🎯 Tu maîtrises maintenant les bases du pwning !\n\n");
    
    // Sur Windows, on peut lancer cmd au lieu de /bin/sh
    printf("🚀 Lancement d'un shell...\n");
    system("cmd.exe");
}

// Fonction vulnérable
void vulnerable_function() {
    char buffer[64];  // Buffer de 64 bytes
    
    printf("🎯 Challenge 1: Buffer Overflow Basique (Windows)\n");
    printf("📝 Entre ton input (max conseillé: 63 caractères): ");
    
    // VULNÉRABILITÉ: gets() ne vérifie pas la taille du buffer !
    // Note: gets() est deprecated mais parfait pour apprendre
    fflush(stdout);
    gets(buffer);
    
    printf("Tu as entré: %s\n", buffer);
    printf("❌ Fonction normale terminée, pas de chance !\n");
}

// Fonction pour afficher des informations utiles
void show_info() {
    printf("=== FORMATION PWN - CHALLENGE 1 WINDOWS ===\n");
    printf("Objectif: Faire exécuter la fonction win()\n");
    printf("💡 Indice: Le buffer fait 64 bytes...\n");
    printf("📍 Adresse de win(): %p\n", (void*)win);
    printf("📍 Adresse de vulnerable_function(): %p\n", (void*)vulnerable_function);
    printf("📍 Adresse de buffer (approximative): %p\n", (void*)&vulnerable_function + 100);
    printf("==========================================\n\n");
}

int main() {
    // Désactiver le buffering pour un meilleur affichage
    setvbuf(stdout, NULL, _IONBF, 0);
    setvbuf(stdin, NULL, _IONBF, 0);
    
    show_info();
    vulnerable_function();
    
    printf("\n🔚 Programme terminé normalement.\n");
    return 0;
}

/*
 * NOTES PÉDAGOGIQUES POUR WINDOWS:
 * 
 * 1. COMPILATION:
 *    gcc -g -fno-stack-protector -O0 windows_level1.c -o windows_level1.exe
 *    
 *    Options importantes:
 *    -g : Symboles de debug
 *    -fno-stack-protector : Désactive les canaries
 *    -O0 : Pas d'optimisation
 * 
 * 2. DIFFÉRENCES WINDOWS vs LINUX:
 *    - Calling convention différente (Microsoft x64 vs System V)
 *    - Adresses mémoire différentes
 *    - cmd.exe au lieu de /bin/sh
 *    - Pas de NX par défaut sur certaines versions
 * 
 * 3. EXPLOITATION:
 *    - Même principe: déborder le buffer pour contrôler RIP
 *    - Calculer l'offset avec un pattern cyclique
 *    - Remplacer l'adresse de retour par l'adresse de win()
 * 
 * 4. ANALYSE AVEC GDB (si disponible):
 *    gdb windows_level1.exe
 *    break vulnerable_function
 *    run
 *    disas vulnerable_function
 *    x/20gx $rsp
 * 
 * 5. ALTERNATIVE SANS GDB:
 *    - Utiliser des printf pour débugger
 *    - Tester avec des inputs de taille croissante
 *    - Observer les crashes
 * 
 * 6. PAYLOAD CONSTRUCTION:
 *    - Trouver l'offset (généralement 64 + 8 = 72 bytes)
 *    - payload = b'A' * offset + adresse_win (little-endian)
 *    - Sur Windows 64-bit: 8 bytes pour l'adresse
 */
