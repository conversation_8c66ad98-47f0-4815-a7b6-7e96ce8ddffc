# Script PowerShell pour installer MinGW-w64 et configurer l'environnement de pwning

Write-Host "🚀 Installation de MinGW-w64 pour la formation PWN" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Vérifier si chocolatey est installé
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "📦 Installation de Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    # Recharger l'environnement
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
}

# Installer MinGW-w64
Write-Host "🔧 Installation de MinGW-w64..." -ForegroundColor Yellow
choco install mingw -y

# Installer Git (si pas déjà installé)
Write-Host "📚 Installation de Git..." -ForegroundColor Yellow
choco install git -y

# Recharger l'environnement PATH
Write-Host "🔄 Rechargement de l'environnement..." -ForegroundColor Yellow
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

# Vérifier l'installation
Write-Host "✅ Vérification de l'installation..." -ForegroundColor Green
try {
    $gccVersion = gcc --version 2>$null
    if ($gccVersion) {
        Write-Host "✅ GCC installé avec succès !" -ForegroundColor Green
        Write-Host $gccVersion[0] -ForegroundColor Cyan
    } else {
        Write-Host "❌ Erreur: GCC non trouvé dans le PATH" -ForegroundColor Red
        Write-Host "💡 Redémarre PowerShell et réessaie" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Erreur lors de la vérification de GCC" -ForegroundColor Red
}

Write-Host "`n🎯 Installation terminée !" -ForegroundColor Green
Write-Host "📝 Prochaines étapes :" -ForegroundColor Yellow
Write-Host "1. Redémarre PowerShell" -ForegroundColor White
Write-Host "2. Teste avec: gcc --version" -ForegroundColor White
Write-Host "3. Compile ton premier challenge !" -ForegroundColor White
