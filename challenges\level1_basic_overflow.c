/*
 * CHALLENGE 1: Buffer Overflow Basique
 * Objectif: Comprendre et exploiter un buffer overflow simple
 * Difficulté: Débutant
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// Fonction secrète que nous voulons exécuter
void win() {
    printf("🎉 Félicitations ! Tu as réussi à exploiter le buffer overflow !\n");
    printf("🏆 FLAG: CTF{basic_buffer_overflow_mastered}\n");
    system("/bin/sh");  // Shell pour explorer
}

// Fonction vulnérable
void vulnerable_function() {
    char buffer[64];  // Buffer de 64 bytes
    
    printf("🎯 Challenge 1: Buffer Overflow Basique\n");
    printf("📝 Entre ton input: ");
    
    // VULNÉRABILITÉ: gets() ne vérifie pas la taille du buffer !
    gets(buffer);
    
    printf("Tu as entré: %s\n", buffer);
    printf("❌ Pas de chance, essaie encore !\n");
}

int main() {
    printf("=== FORMATION PWN - CHALLENGE 1 ===\n");
    printf("Objectif: Faire exécuter la fonction win()\n");
    printf("Indice: Le buffer fait 64 bytes...\n");
    printf("Adresse de win(): %p\n", win);
    printf("=====================================\n\n");
    
    vulnerable_function();
    
    return 0;
}

/*
 * NOTES PÉDAGOGIQUES:
 * 
 * 1. VULNÉRABILITÉ:
 *    - gets() ne vérifie pas la taille du buffer
 *    - On peut écrire au-delà des 64 bytes alloués
 *    - Cela permet d'écraser l'adresse de retour sur la stack
 * 
 * 2. EXPLOITATION:
 *    - Trouver l'offset pour écraser l'adresse de retour
 *    - Remplacer cette adresse par l'adresse de win()
 *    - Payload: 'A' * offset + adresse_de_win
 * 
 * 3. COMPILATION:
 *    gcc -g -fno-stack-protector -z execstack -no-pie level1_basic_overflow.c -o level1
 * 
 * 4. ANALYSE:
 *    - Utiliser GDB pour analyser la stack
 *    - Utiliser pattern_create/pattern_offset pour trouver l'offset
 *    - Vérifier avec checksec que les protections sont désactivées
 */
